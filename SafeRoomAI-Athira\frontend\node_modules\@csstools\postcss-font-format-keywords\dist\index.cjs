"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=e(require("postcss-value-parser"));const o=["woff","truetype","opentype","woff2","embedded-opentype","collection","svg"],r=e=>{const r="preserve"in Object(e)&&Boolean(e.preserve);return{postcssPlugin:"postcss-font-format-keywords",AtRule(e){"font-face"===e.name.toLowerCase()&&e.walkDecls((e=>{if("src"!==e.prop.toLowerCase())return;if(!e.value.toLowerCase().includes("format("))return;const s=t.default(e.value);s.walk((e=>{"function"===e.type&&"format"===e.value.toLowerCase()&&e.nodes.forEach((e=>{"word"===e.type&&o.includes(e.value.toLowerCase())&&(e.value=t.default.stringify({type:"string",value:e.value,quote:'"'}))}))})),s.toString()!==e.value&&(e.cloneBefore({value:s.toString()}),r||e.remove())}))}}};r.postcss=!0,module.exports=r;
