{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\capstone final project\\\\SafeRoomAI-Athira\\\\frontend\\\\src\\\\components\\\\AIFeedbackButtons.jsx\",\n  _s = $RefreshSig$();\n// src/components/AIFeedbackButtons.jsx\nimport React, { useState } from 'react';\nimport { Box, IconButton, Tooltip, Chip, Typography, Fade, CircularProgress, Button, Paper } from '@mui/material';\nimport { ThumbUp, ThumbDown, Feedback, CheckCircle, Cancel } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AIFeedbackButtons = ({\n  suggestionId,\n  suggestionType = 'anomaly',\n  onFeedback,\n  size = 'small',\n  variant = 'default',\n  // 'default', 'compact', 'inline'\n  disabled = false,\n  initialFeedback = null\n}) => {\n  _s();\n  const [feedback, setFeedback] = useState(initialFeedback);\n  const [loading, setLoading] = useState(false);\n  const [showConfirmation, setShowConfirmation] = useState(false);\n  const handleFeedback = async (type, event) => {\n    // Simple event prevention\n    if (event) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n    if (disabled || loading) return;\n    setLoading(true);\n    try {\n      // Call the parent callback\n      if (onFeedback) {\n        await onFeedback(suggestionId, type, suggestionType, event);\n      }\n      setFeedback(type);\n      setShowConfirmation(true);\n\n      // Hide confirmation after 2 seconds\n      setTimeout(() => {\n        setShowConfirmation(false);\n      }, 2000);\n    } catch (error) {\n      console.error('Failed to submit feedback:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getFeedbackColor = type => {\n    if (feedback === type) {\n      return type === 'accept' ? 'success' : 'error';\n    }\n    return 'default';\n  };\n  const getFeedbackIcon = type => {\n    if (loading && feedback === type) {\n      return /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 16\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 14\n      }, this);\n    }\n    return type === 'accept' ? /*#__PURE__*/_jsxDEV(ThumbUp, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 32\n    }, this) : /*#__PURE__*/_jsxDEV(ThumbDown, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 46\n    }, this);\n  };\n\n  // Compact variant for tight spaces - GitHub-style\n  if (variant === 'compact') {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      alignItems: \"center\",\n      gap: 0.5,\n      sx: {\n        p: 0.5,\n        borderRadius: 1,\n        backgroundColor: showConfirmation ? 'success.light' : 'transparent',\n        transition: 'all 0.2s ease'\n      },\n      children: showConfirmation ? /*#__PURE__*/_jsxDEV(Fade, {\n        in: showConfirmation,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 0.5,\n          children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n            fontSize: \"small\",\n            color: \"success\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"success.dark\",\n            fontWeight: 600,\n            children: feedback === 'accept' ? 'Helpful!' : 'Thanks!'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 0.5,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: feedback === 'accept' ? 'contained' : 'outlined',\n          color: \"success\",\n          startIcon: loading && feedback === 'accept' ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 12\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 61\n          }, this) : /*#__PURE__*/_jsxDEV(ThumbUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 94\n          }, this),\n          onClick: e => handleFeedback('accept', e),\n          disabled: disabled || loading,\n          sx: {\n            minWidth: 'auto',\n            px: 1,\n            py: 0.25,\n            fontSize: '0.7rem',\n            fontWeight: 600,\n            textTransform: 'none',\n            borderRadius: 1,\n            '&:hover': {\n              transform: 'translateY(-1px)',\n              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n            }\n          },\n          children: \"Yes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: feedback === 'reject' ? 'contained' : 'outlined',\n          color: \"error\",\n          startIcon: loading && feedback === 'reject' ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 12\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 61\n          }, this) : /*#__PURE__*/_jsxDEV(ThumbDown, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 94\n          }, this),\n          onClick: e => handleFeedback('reject', e),\n          disabled: disabled || loading,\n          sx: {\n            minWidth: 'auto',\n            px: 1,\n            py: 0.25,\n            fontSize: '0.7rem',\n            fontWeight: 600,\n            textTransform: 'none',\n            borderRadius: 1,\n            '&:hover': {\n              transform: 'translateY(-1px)',\n              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n            }\n          },\n          children: \"No\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Inline variant for integration within text/content\n  if (variant === 'inline') {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"inline-flex\",\n      alignItems: \"center\",\n      gap: 1,\n      ml: 1,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        children: \"Helpful?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 0.5,\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: e => handleFeedback('accept', e),\n          disabled: disabled || loading,\n          color: getFeedbackColor('accept'),\n          sx: {\n            p: 0.25\n          },\n          children: getFeedbackIcon('accept')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: e => handleFeedback('reject', e),\n          disabled: disabled || loading,\n          color: getFeedbackColor('reject'),\n          sx: {\n            p: 0.25\n          },\n          children: getFeedbackIcon('reject')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Default variant - GitHub-style suggestion interface\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 0,\n    sx: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: 1,\n      p: 1.5,\n      borderRadius: 2,\n      backgroundColor: 'background.paper',\n      border: '1px solid',\n      borderColor: 'divider',\n      transition: 'all 0.2s ease',\n      '&:hover': {\n        borderColor: 'primary.main',\n        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(Feedback, {\n      fontSize: \"small\",\n      color: \"action\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      color: \"text.secondary\",\n      sx: {\n        mr: 1,\n        fontWeight: 500\n      },\n      children: \"Was this AI suggestion helpful?\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 7\n    }, this), showConfirmation ? /*#__PURE__*/_jsxDEV(Fade, {\n      in: showConfirmation,\n      children: /*#__PURE__*/_jsxDEV(Chip, {\n        icon: feedback === 'accept' ? /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 43\n        }, this) : /*#__PURE__*/_jsxDEV(Cancel, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 61\n        }, this),\n        label: feedback === 'accept' ? 'Thanks for the feedback!' : 'Thanks, we\\'ll improve this',\n        size: \"small\",\n        color: feedback === 'accept' ? 'success' : 'warning',\n        sx: {\n          fontWeight: 500,\n          '& .MuiChip-icon': {\n            fontSize: '16px'\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      gap: 1,\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        variant: feedback === 'accept' ? 'contained' : 'outlined',\n        color: \"success\",\n        startIcon: loading && feedback === 'accept' ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 14\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 59\n        }, this) : /*#__PURE__*/_jsxDEV(ThumbUp, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 92\n        }, this),\n        onClick: e => handleFeedback('accept', e),\n        disabled: disabled || loading,\n        sx: {\n          minWidth: 'auto',\n          px: 1.5,\n          py: 0.5,\n          fontSize: '0.75rem',\n          fontWeight: 600,\n          textTransform: 'none',\n          borderRadius: 1.5,\n          '&:hover': {\n            transform: 'translateY(-1px)',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }\n        },\n        children: \"Yes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        variant: feedback === 'reject' ? 'contained' : 'outlined',\n        color: \"error\",\n        startIcon: loading && feedback === 'reject' ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 14\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 59\n        }, this) : /*#__PURE__*/_jsxDEV(ThumbDown, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 92\n        }, this),\n        onClick: e => handleFeedback('reject', e),\n        disabled: disabled || loading,\n        sx: {\n          minWidth: 'auto',\n          px: 1.5,\n          py: 0.5,\n          fontSize: '0.75rem',\n          fontWeight: 600,\n          textTransform: 'none',\n          borderRadius: 1.5,\n          '&:hover': {\n            transform: 'translateY(-1px)',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          }\n        },\n        children: \"No\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 5\n  }, this);\n};\n_s(AIFeedbackButtons, \"RYb58Kxd2QiraAr9l+GzVhScCcE=\");\n_c = AIFeedbackButtons;\nexport default AIFeedbackButtons;\nvar _c;\n$RefreshReg$(_c, \"AIFeedbackButtons\");", "map": {"version": 3, "names": ["React", "useState", "Box", "IconButton", "<PERSON><PERSON><PERSON>", "Chip", "Typography", "Fade", "CircularProgress", "<PERSON><PERSON>", "Paper", "ThumbUp", "ThumbDown", "<PERSON><PERSON><PERSON>", "CheckCircle", "Cancel", "jsxDEV", "_jsxDEV", "AIFeedbackButtons", "suggestionId", "suggestionType", "onFeedback", "size", "variant", "disabled", "initialFeedback", "_s", "feedback", "setFeedback", "loading", "setLoading", "showConfirmation", "setShowConfirmation", "handleFeedback", "type", "event", "preventDefault", "stopPropagation", "setTimeout", "error", "console", "getFeedbackColor", "getFeedbackIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "gap", "sx", "p", "borderRadius", "backgroundColor", "transition", "children", "in", "fontSize", "color", "fontWeight", "startIcon", "onClick", "e", "min<PERSON><PERSON><PERSON>", "px", "py", "textTransform", "transform", "boxShadow", "ml", "elevation", "border", "borderColor", "mr", "icon", "label", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/capstone final project/SafeRoomAI-Athira/frontend/src/components/AIFeedbackButtons.jsx"], "sourcesContent": ["// src/components/AIFeedbackButtons.jsx\nimport React, { useState } from 'react';\nimport {\n  Box,\n  IconButton,\n  Tooltip,\n  Chip,\n  Typography,\n  Fade,\n  CircularProgress,\n  Button,\n  Paper\n} from '@mui/material';\nimport {\n  ThumbUp,\n  ThumbDown,\n  Feedback,\n  CheckCircle,\n  Cancel\n} from '@mui/icons-material';\n\nconst AIFeedbackButtons = ({ \n  suggestionId, \n  suggestionType = 'anomaly',\n  onFeedback,\n  size = 'small',\n  variant = 'default', // 'default', 'compact', 'inline'\n  disabled = false,\n  initialFeedback = null\n}) => {\n  const [feedback, setFeedback] = useState(initialFeedback);\n  const [loading, setLoading] = useState(false);\n  const [showConfirmation, setShowConfirmation] = useState(false);\n\n  const handleFeedback = async (type, event) => {\n    // Simple event prevention\n    if (event) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n\n    if (disabled || loading) return;\n\n    setLoading(true);\n    try {\n      // Call the parent callback\n      if (onFeedback) {\n        await onFeedback(suggestionId, type, suggestionType, event);\n      }\n\n      setFeedback(type);\n      setShowConfirmation(true);\n\n      // Hide confirmation after 2 seconds\n      setTimeout(() => {\n        setShowConfirmation(false);\n      }, 2000);\n\n    } catch (error) {\n      console.error('Failed to submit feedback:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getFeedbackColor = (type) => {\n    if (feedback === type) {\n      return type === 'accept' ? 'success' : 'error';\n    }\n    return 'default';\n  };\n\n  const getFeedbackIcon = (type) => {\n    if (loading && feedback === type) {\n      return <CircularProgress size={16} />;\n    }\n    return type === 'accept' ? <ThumbUp /> : <ThumbDown />;\n  };\n\n  // Compact variant for tight spaces - GitHub-style\n  if (variant === 'compact') {\n    return (\n      <Box\n        display=\"flex\"\n        alignItems=\"center\"\n        gap={0.5}\n        sx={{\n          p: 0.5,\n          borderRadius: 1,\n          backgroundColor: showConfirmation ? 'success.light' : 'transparent',\n          transition: 'all 0.2s ease'\n        }}\n      >\n        {showConfirmation ? (\n          <Fade in={showConfirmation}>\n            <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n              <CheckCircle fontSize=\"small\" color=\"success\" />\n              <Typography variant=\"caption\" color=\"success.dark\" fontWeight={600}>\n                {feedback === 'accept' ? 'Helpful!' : 'Thanks!'}\n              </Typography>\n            </Box>\n          </Fade>\n        ) : (\n          <Box display=\"flex\" gap={0.5}>\n            <Button\n              size=\"small\"\n              variant={feedback === 'accept' ? 'contained' : 'outlined'}\n              color=\"success\"\n              startIcon={loading && feedback === 'accept' ? <CircularProgress size={12} /> : <ThumbUp />}\n              onClick={(e) => handleFeedback('accept', e)}\n              disabled={disabled || loading}\n              sx={{\n                minWidth: 'auto',\n                px: 1,\n                py: 0.25,\n                fontSize: '0.7rem',\n                fontWeight: 600,\n                textTransform: 'none',\n                borderRadius: 1,\n                '&:hover': {\n                  transform: 'translateY(-1px)',\n                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                }\n              }}\n            >\n              Yes\n            </Button>\n            <Button\n              size=\"small\"\n              variant={feedback === 'reject' ? 'contained' : 'outlined'}\n              color=\"error\"\n              startIcon={loading && feedback === 'reject' ? <CircularProgress size={12} /> : <ThumbDown />}\n              onClick={(e) => handleFeedback('reject', e)}\n              disabled={disabled || loading}\n              sx={{\n                minWidth: 'auto',\n                px: 1,\n                py: 0.25,\n                fontSize: '0.7rem',\n                fontWeight: 600,\n                textTransform: 'none',\n                borderRadius: 1,\n                '&:hover': {\n                  transform: 'translateY(-1px)',\n                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                }\n              }}\n            >\n              No\n            </Button>\n          </Box>\n        )}\n      </Box>\n    );\n  }\n\n  // Inline variant for integration within text/content\n  if (variant === 'inline') {\n    return (\n      <Box display=\"inline-flex\" alignItems=\"center\" gap={1} ml={1}>\n        <Typography variant=\"caption\" color=\"text.secondary\">\n          Helpful?\n        </Typography>\n        <Box display=\"flex\" gap={0.5}>\n          <IconButton\n            size=\"small\"\n            onClick={(e) => handleFeedback('accept', e)}\n            disabled={disabled || loading}\n            color={getFeedbackColor('accept')}\n            sx={{ p: 0.25 }}\n          >\n            {getFeedbackIcon('accept')}\n          </IconButton>\n          <IconButton\n            size=\"small\"\n            onClick={(e) => handleFeedback('reject', e)}\n            disabled={disabled || loading}\n            color={getFeedbackColor('reject')}\n            sx={{ p: 0.25 }}\n          >\n            {getFeedbackIcon('reject')}\n          </IconButton>\n        </Box>\n      </Box>\n    );\n  }\n\n  // Default variant - GitHub-style suggestion interface\n  return (\n    <Paper\n      elevation={0}\n      sx={{\n        display: 'flex',\n        alignItems: 'center',\n        gap: 1,\n        p: 1.5,\n        borderRadius: 2,\n        backgroundColor: 'background.paper',\n        border: '1px solid',\n        borderColor: 'divider',\n        transition: 'all 0.2s ease',\n        '&:hover': {\n          borderColor: 'primary.main',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n        }\n      }}\n    >\n      <Feedback fontSize=\"small\" color=\"action\" />\n      <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mr: 1, fontWeight: 500 }}>\n        Was this AI suggestion helpful?\n      </Typography>\n\n      {showConfirmation ? (\n        <Fade in={showConfirmation}>\n          <Chip\n            icon={feedback === 'accept' ? <CheckCircle /> : <Cancel />}\n            label={feedback === 'accept' ? 'Thanks for the feedback!' : 'Thanks, we\\'ll improve this'}\n            size=\"small\"\n            color={feedback === 'accept' ? 'success' : 'warning'}\n            sx={{\n              fontWeight: 500,\n              '& .MuiChip-icon': {\n                fontSize: '16px'\n              }\n            }}\n          />\n        </Fade>\n      ) : (\n        <Box display=\"flex\" gap={1}>\n          <Button\n            size=\"small\"\n            variant={feedback === 'accept' ? 'contained' : 'outlined'}\n            color=\"success\"\n            startIcon={loading && feedback === 'accept' ? <CircularProgress size={14} /> : <ThumbUp />}\n            onClick={(e) => handleFeedback('accept', e)}\n            disabled={disabled || loading}\n            sx={{\n              minWidth: 'auto',\n              px: 1.5,\n              py: 0.5,\n              fontSize: '0.75rem',\n              fontWeight: 600,\n              textTransform: 'none',\n              borderRadius: 1.5,\n              '&:hover': {\n                transform: 'translateY(-1px)',\n                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n              }\n            }}\n          >\n            Yes\n          </Button>\n          <Button\n            size=\"small\"\n            variant={feedback === 'reject' ? 'contained' : 'outlined'}\n            color=\"error\"\n            startIcon={loading && feedback === 'reject' ? <CircularProgress size={14} /> : <ThumbDown />}\n            onClick={(e) => handleFeedback('reject', e)}\n            disabled={disabled || loading}\n            sx={{\n              minWidth: 'auto',\n              px: 1.5,\n              py: 0.5,\n              fontSize: '0.75rem',\n              fontWeight: 600,\n              textTransform: 'none',\n              borderRadius: 1.5,\n              '&:hover': {\n                transform: 'translateY(-1px)',\n                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n              }\n            }}\n          >\n            No\n          </Button>\n        </Box>\n      )}\n    </Paper>\n  );\n};\n\nexport default AIFeedbackButtons;\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,gBAAgB,EAChBC,MAAM,EACNC,KAAK,QACA,eAAe;AACtB,SACEC,OAAO,EACPC,SAAS,EACTC,QAAQ,EACRC,WAAW,EACXC,MAAM,QACD,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,YAAY;EACZC,cAAc,GAAG,SAAS;EAC1BC,UAAU;EACVC,IAAI,GAAG,OAAO;EACdC,OAAO,GAAG,SAAS;EAAE;EACrBC,QAAQ,GAAG,KAAK;EAChBC,eAAe,GAAG;AACpB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAACwB,eAAe,CAAC;EACzD,MAAM,CAACI,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAE/D,MAAMgC,cAAc,GAAG,MAAAA,CAAOC,IAAI,EAAEC,KAAK,KAAK;IAC5C;IACA,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACC,cAAc,CAAC,CAAC;MACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IACzB;IAEA,IAAIb,QAAQ,IAAIK,OAAO,EAAE;IAEzBC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,IAAIT,UAAU,EAAE;QACd,MAAMA,UAAU,CAACF,YAAY,EAAEe,IAAI,EAAEd,cAAc,EAAEe,KAAK,CAAC;MAC7D;MAEAP,WAAW,CAACM,IAAI,CAAC;MACjBF,mBAAmB,CAAC,IAAI,CAAC;;MAEzB;MACAM,UAAU,CAAC,MAAM;QACfN,mBAAmB,CAAC,KAAK,CAAC;MAC5B,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,gBAAgB,GAAIP,IAAI,IAAK;IACjC,IAAIP,QAAQ,KAAKO,IAAI,EAAE;MACrB,OAAOA,IAAI,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAO;IAChD;IACA,OAAO,SAAS;EAClB,CAAC;EAED,MAAMQ,eAAe,GAAIR,IAAI,IAAK;IAChC,IAAIL,OAAO,IAAIF,QAAQ,KAAKO,IAAI,EAAE;MAChC,oBAAOjB,OAAA,CAACT,gBAAgB;QAACc,IAAI,EAAE;MAAG;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACvC;IACA,OAAOZ,IAAI,KAAK,QAAQ,gBAAGjB,OAAA,CAACN,OAAO;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAG7B,OAAA,CAACL,SAAS;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxD,CAAC;;EAED;EACA,IAAIvB,OAAO,KAAK,SAAS,EAAE;IACzB,oBACEN,OAAA,CAACf,GAAG;MACF6C,OAAO,EAAC,MAAM;MACdC,UAAU,EAAC,QAAQ;MACnBC,GAAG,EAAE,GAAI;MACTC,EAAE,EAAE;QACFC,CAAC,EAAE,GAAG;QACNC,YAAY,EAAE,CAAC;QACfC,eAAe,EAAEtB,gBAAgB,GAAG,eAAe,GAAG,aAAa;QACnEuB,UAAU,EAAE;MACd,CAAE;MAAAC,QAAA,EAEDxB,gBAAgB,gBACfd,OAAA,CAACV,IAAI;QAACiD,EAAE,EAAEzB,gBAAiB;QAAAwB,QAAA,eACzBtC,OAAA,CAACf,GAAG;UAAC6C,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,GAAI;UAAAM,QAAA,gBAC/CtC,OAAA,CAACH,WAAW;YAAC2C,QAAQ,EAAC,OAAO;YAACC,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChD7B,OAAA,CAACX,UAAU;YAACiB,OAAO,EAAC,SAAS;YAACmC,KAAK,EAAC,cAAc;YAACC,UAAU,EAAE,GAAI;YAAAJ,QAAA,EAChE5B,QAAQ,KAAK,QAAQ,GAAG,UAAU,GAAG;UAAS;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,gBAEP7B,OAAA,CAACf,GAAG;QAAC6C,OAAO,EAAC,MAAM;QAACE,GAAG,EAAE,GAAI;QAAAM,QAAA,gBAC3BtC,OAAA,CAACR,MAAM;UACLa,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEI,QAAQ,KAAK,QAAQ,GAAG,WAAW,GAAG,UAAW;UAC1D+B,KAAK,EAAC,SAAS;UACfE,SAAS,EAAE/B,OAAO,IAAIF,QAAQ,KAAK,QAAQ,gBAAGV,OAAA,CAACT,gBAAgB;YAACc,IAAI,EAAE;UAAG;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7B,OAAA,CAACN,OAAO;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3Fe,OAAO,EAAGC,CAAC,IAAK7B,cAAc,CAAC,QAAQ,EAAE6B,CAAC,CAAE;UAC5CtC,QAAQ,EAAEA,QAAQ,IAAIK,OAAQ;UAC9BqB,EAAE,EAAE;YACFa,QAAQ,EAAE,MAAM;YAChBC,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,IAAI;YACRR,QAAQ,EAAE,QAAQ;YAClBE,UAAU,EAAE,GAAG;YACfO,aAAa,EAAE,MAAM;YACrBd,YAAY,EAAE,CAAC;YACf,SAAS,EAAE;cACTe,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb;UACF,CAAE;UAAAb,QAAA,EACH;QAED;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7B,OAAA,CAACR,MAAM;UACLa,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEI,QAAQ,KAAK,QAAQ,GAAG,WAAW,GAAG,UAAW;UAC1D+B,KAAK,EAAC,OAAO;UACbE,SAAS,EAAE/B,OAAO,IAAIF,QAAQ,KAAK,QAAQ,gBAAGV,OAAA,CAACT,gBAAgB;YAACc,IAAI,EAAE;UAAG;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7B,OAAA,CAACL,SAAS;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7Fe,OAAO,EAAGC,CAAC,IAAK7B,cAAc,CAAC,QAAQ,EAAE6B,CAAC,CAAE;UAC5CtC,QAAQ,EAAEA,QAAQ,IAAIK,OAAQ;UAC9BqB,EAAE,EAAE;YACFa,QAAQ,EAAE,MAAM;YAChBC,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,IAAI;YACRR,QAAQ,EAAE,QAAQ;YAClBE,UAAU,EAAE,GAAG;YACfO,aAAa,EAAE,MAAM;YACrBd,YAAY,EAAE,CAAC;YACf,SAAS,EAAE;cACTe,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb;UACF,CAAE;UAAAb,QAAA,EACH;QAED;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV;;EAEA;EACA,IAAIvB,OAAO,KAAK,QAAQ,EAAE;IACxB,oBACEN,OAAA,CAACf,GAAG;MAAC6C,OAAO,EAAC,aAAa;MAACC,UAAU,EAAC,QAAQ;MAACC,GAAG,EAAE,CAAE;MAACoB,EAAE,EAAE,CAAE;MAAAd,QAAA,gBAC3DtC,OAAA,CAACX,UAAU;QAACiB,OAAO,EAAC,SAAS;QAACmC,KAAK,EAAC,gBAAgB;QAAAH,QAAA,EAAC;MAErD;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7B,OAAA,CAACf,GAAG;QAAC6C,OAAO,EAAC,MAAM;QAACE,GAAG,EAAE,GAAI;QAAAM,QAAA,gBAC3BtC,OAAA,CAACd,UAAU;UACTmB,IAAI,EAAC,OAAO;UACZuC,OAAO,EAAGC,CAAC,IAAK7B,cAAc,CAAC,QAAQ,EAAE6B,CAAC,CAAE;UAC5CtC,QAAQ,EAAEA,QAAQ,IAAIK,OAAQ;UAC9B6B,KAAK,EAAEjB,gBAAgB,CAAC,QAAQ,CAAE;UAClCS,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAK,CAAE;UAAAI,QAAA,EAEfb,eAAe,CAAC,QAAQ;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACb7B,OAAA,CAACd,UAAU;UACTmB,IAAI,EAAC,OAAO;UACZuC,OAAO,EAAGC,CAAC,IAAK7B,cAAc,CAAC,QAAQ,EAAE6B,CAAC,CAAE;UAC5CtC,QAAQ,EAAEA,QAAQ,IAAIK,OAAQ;UAC9B6B,KAAK,EAAEjB,gBAAgB,CAAC,QAAQ,CAAE;UAClCS,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAK,CAAE;UAAAI,QAAA,EAEfb,eAAe,CAAC,QAAQ;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,oBACE7B,OAAA,CAACP,KAAK;IACJ4D,SAAS,EAAE,CAAE;IACbpB,EAAE,EAAE;MACFH,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE,CAAC;MACNE,CAAC,EAAE,GAAG;MACNC,YAAY,EAAE,CAAC;MACfC,eAAe,EAAE,kBAAkB;MACnCkB,MAAM,EAAE,WAAW;MACnBC,WAAW,EAAE,SAAS;MACtBlB,UAAU,EAAE,eAAe;MAC3B,SAAS,EAAE;QACTkB,WAAW,EAAE,cAAc;QAC3BJ,SAAS,EAAE;MACb;IACF,CAAE;IAAAb,QAAA,gBAEFtC,OAAA,CAACJ,QAAQ;MAAC4C,QAAQ,EAAC,OAAO;MAACC,KAAK,EAAC;IAAQ;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC5C7B,OAAA,CAACX,UAAU;MAACiB,OAAO,EAAC,OAAO;MAACmC,KAAK,EAAC,gBAAgB;MAACR,EAAE,EAAE;QAAEuB,EAAE,EAAE,CAAC;QAAEd,UAAU,EAAE;MAAI,CAAE;MAAAJ,QAAA,EAAC;IAEnF;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZf,gBAAgB,gBACfd,OAAA,CAACV,IAAI;MAACiD,EAAE,EAAEzB,gBAAiB;MAAAwB,QAAA,eACzBtC,OAAA,CAACZ,IAAI;QACHqE,IAAI,EAAE/C,QAAQ,KAAK,QAAQ,gBAAGV,OAAA,CAACH,WAAW;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG7B,OAAA,CAACF,MAAM;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3D6B,KAAK,EAAEhD,QAAQ,KAAK,QAAQ,GAAG,0BAA0B,GAAG,6BAA8B;QAC1FL,IAAI,EAAC,OAAO;QACZoC,KAAK,EAAE/B,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;QACrDuB,EAAE,EAAE;UACFS,UAAU,EAAE,GAAG;UACf,iBAAiB,EAAE;YACjBF,QAAQ,EAAE;UACZ;QACF;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,gBAEP7B,OAAA,CAACf,GAAG;MAAC6C,OAAO,EAAC,MAAM;MAACE,GAAG,EAAE,CAAE;MAAAM,QAAA,gBACzBtC,OAAA,CAACR,MAAM;QACLa,IAAI,EAAC,OAAO;QACZC,OAAO,EAAEI,QAAQ,KAAK,QAAQ,GAAG,WAAW,GAAG,UAAW;QAC1D+B,KAAK,EAAC,SAAS;QACfE,SAAS,EAAE/B,OAAO,IAAIF,QAAQ,KAAK,QAAQ,gBAAGV,OAAA,CAACT,gBAAgB;UAACc,IAAI,EAAE;QAAG;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG7B,OAAA,CAACN,OAAO;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3Fe,OAAO,EAAGC,CAAC,IAAK7B,cAAc,CAAC,QAAQ,EAAE6B,CAAC,CAAE;QAC5CtC,QAAQ,EAAEA,QAAQ,IAAIK,OAAQ;QAC9BqB,EAAE,EAAE;UACFa,QAAQ,EAAE,MAAM;UAChBC,EAAE,EAAE,GAAG;UACPC,EAAE,EAAE,GAAG;UACPR,QAAQ,EAAE,SAAS;UACnBE,UAAU,EAAE,GAAG;UACfO,aAAa,EAAE,MAAM;UACrBd,YAAY,EAAE,GAAG;UACjB,SAAS,EAAE;YACTe,SAAS,EAAE,kBAAkB;YAC7BC,SAAS,EAAE;UACb;QACF,CAAE;QAAAb,QAAA,EACH;MAED;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7B,OAAA,CAACR,MAAM;QACLa,IAAI,EAAC,OAAO;QACZC,OAAO,EAAEI,QAAQ,KAAK,QAAQ,GAAG,WAAW,GAAG,UAAW;QAC1D+B,KAAK,EAAC,OAAO;QACbE,SAAS,EAAE/B,OAAO,IAAIF,QAAQ,KAAK,QAAQ,gBAAGV,OAAA,CAACT,gBAAgB;UAACc,IAAI,EAAE;QAAG;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG7B,OAAA,CAACL,SAAS;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7Fe,OAAO,EAAGC,CAAC,IAAK7B,cAAc,CAAC,QAAQ,EAAE6B,CAAC,CAAE;QAC5CtC,QAAQ,EAAEA,QAAQ,IAAIK,OAAQ;QAC9BqB,EAAE,EAAE;UACFa,QAAQ,EAAE,MAAM;UAChBC,EAAE,EAAE,GAAG;UACPC,EAAE,EAAE,GAAG;UACPR,QAAQ,EAAE,SAAS;UACnBE,UAAU,EAAE,GAAG;UACfO,aAAa,EAAE,MAAM;UACrBd,YAAY,EAAE,GAAG;UACjB,SAAS,EAAE;YACTe,SAAS,EAAE,kBAAkB;YAC7BC,SAAS,EAAE;UACb;QACF,CAAE;QAAAb,QAAA,EACH;MAED;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEZ,CAAC;AAACpB,EAAA,CAlQIR,iBAAiB;AAAA0D,EAAA,GAAjB1D,iBAAiB;AAoQvB,eAAeA,iBAAiB;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}