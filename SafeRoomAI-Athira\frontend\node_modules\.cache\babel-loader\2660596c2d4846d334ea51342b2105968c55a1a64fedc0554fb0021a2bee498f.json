{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\capstone final project\\\\SafeRoomAI-Athira\\\\frontend\\\\src\\\\screens\\\\Analytics.jsx\",\n  _s = $RefreshSig$();\n// src/screens/Analytics.jsx\n\nimport React, { useState, useEffect } from 'react';\nimport { Container, Typography, Grid, Card, CardContent, Box, IconButton, FormControl, InputLabel, Select, MenuItem, Chip } from '@mui/material';\nimport { Refresh, TrendingUp, Warning, Assessment, Timeline } from '@mui/icons-material';\nimport { LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';\nimport '../App.css';\nimport { format } from 'date-fns';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorAlert from '../components/ErrorAlert';\nimport StatsCard from '../components/StatsCard';\nimport AnomalyChart from '../components/anomalychart';\nimport AnomalyHeatmap from '../components/anomalyheatmap';\nimport AnomalySnapshots from '../components/anomalysnapshots';\nimport AIFeedbackButtons from '../components/AIFeedbackButtons';\nimport useFeedbackState from '../hooks/useFeedbackState';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Analytics() {\n  _s();\n  const [summary, setSummary] = useState([]);\n  const [errors, setErrors] = useState([]);\n  const [errorMsg, setErrorMsg] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [timeRange, setTimeRange] = useState('24h');\n\n  // Use the feedback state hook\n  const {\n    feedbackStates,\n    submitFeedback\n  } = useFeedbackState('analytics');\n  useEffect(() => {\n    fetchData();\n  }, []);\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      // Fetch anomalies‐per‐minute summary\n      const res1 = await fetch('/predict/analytics/summary');\n      if (!res1.ok) throw new Error(`Summary HTTP ${res1.status}`);\n      const summaryJson = await res1.json();\n      if (summaryJson && typeof summaryJson === 'object' && !Array.isArray(summaryJson)) {\n        const summaryArr = Object.entries(summaryJson).map(([time, count]) => ({\n          time,\n          count,\n          formattedTime: format(new Date(time), 'HH:mm')\n        }));\n        setSummary(summaryArr);\n      } else {\n        setSummary([]);\n      }\n\n      // Fetch recent reconstruction errors\n      const res2 = await fetch('/predict/analytics/errors');\n      if (!res2.ok) throw new Error(`Errors HTTP ${res2.status}`);\n      const errorsJson = await res2.json();\n      if (Array.isArray(errorsJson)) {\n        setErrors(errorsJson);\n      } else {\n        setErrors([]);\n      }\n      setErrorMsg(null);\n    } catch (e) {\n      console.error('Analytics fetch error:', e);\n      setErrorMsg('Failed to load analytics data.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Calculate statistics\n  const totalAnomalies = summary.reduce((sum, item) => sum + item.count, 0);\n  const avgErrorRate = errors.length > 0 ? (errors.reduce((sum, err) => sum + err, 0) / errors.length).toFixed(4) : 0;\n  const maxAnomaliesPerMinute = summary.length > 0 ? Math.max(...summary.map(item => item.count)) : 0;\n\n  // Prepare error distribution data\n  const errorDistribution = errors.map((error, index) => ({\n    index: index + 1,\n    value: parseFloat(error.toFixed(4)),\n    category: error > 0.5 ? 'High' : error > 0.2 ? 'Medium' : 'Low'\n  }));\n  const handleFeedback = async (suggestionId, feedbackType, suggestionType) => {\n    await submitFeedback(suggestionId, feedbackType, suggestionType, {\n      analytics_data: {\n        total_anomalies: totalAnomalies,\n        avg_error_rate: avgErrorRate,\n        time_range: timeRange\n      }\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      message: \"Loading analytics data...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      py: 2\n    },\n    className: \"analytics-container\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 4,\n      className: \"analytics-section\",\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        fontWeight: \"bold\",\n        children: \"Analytics Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"Real-time insights and anomaly detection metrics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      gap: 2,\n      mb: 3,\n      flexWrap: \"wrap\",\n      alignItems: \"center\",\n      children: [/*#__PURE__*/_jsxDEV(FormControl, {\n        size: \"small\",\n        sx: {\n          minWidth: 120\n        },\n        children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n          children: \"Time Range\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: timeRange,\n          label: \"Time Range\",\n          onChange: e => setTimeRange(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"1h\",\n            children: \"Last Hour\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"24h\",\n            children: \"Last 24 Hours\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"7d\",\n            children: \"Last 7 Days\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: fetchData,\n        disabled: loading,\n        children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        icon: /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 17\n        }, this),\n        label: `${summary.length} data points`,\n        color: \"primary\",\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), errorMsg && /*#__PURE__*/_jsxDEV(ErrorAlert, {\n      message: errorMsg,\n      onRetry: fetchData,\n      sx: {\n        mb: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 4,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(StatsCard, {\n            title: \"Total Anomalies\",\n            value: totalAnomalies,\n            subtitle: \"Detected events\",\n            icon: /*#__PURE__*/_jsxDEV(Warning, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 19\n            }, this),\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(StatsCard, {\n            title: \"Avg Error Rate\",\n            value: avgErrorRate,\n            subtitle: \"Reconstruction error\",\n            icon: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 19\n            }, this),\n            color: \"error\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(StatsCard, {\n            title: \"Peak Activity\",\n            value: maxAnomaliesPerMinute,\n            subtitle: \"Max per minute\",\n            icon: /*#__PURE__*/_jsxDEV(Timeline, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 19\n            }, this),\n            color: \"info\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(StatsCard, {\n            title: \"Data Points\",\n            value: errors.length,\n            subtitle: \"Error samples\",\n            icon: /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 19\n            }, this),\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          p: 2,\n          backgroundColor: 'background.default',\n          border: feedbackStates['analytics_summary'] === 'accept' ? '2px solid #10b981' : feedbackStates['analytics_summary'] === 'reject' ? '2px solid #ef4444' : '1px solid',\n          borderColor: feedbackStates['analytics_summary'] ? 'transparent' : 'divider',\n          transition: 'all 0.3s ease'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"space-between\",\n          flexWrap: \"wrap\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 1,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"AI Analytics Insights\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this), feedbackStates['analytics_summary'] && /*#__PURE__*/_jsxDEV(Chip, {\n                size: \"small\",\n                label: feedbackStates['analytics_summary'] === 'accept' ? 'Helpful' : 'Needs improvement',\n                color: feedbackStates['analytics_summary'] === 'accept' ? 'success' : 'warning',\n                sx: {\n                  fontSize: '0.7rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"These metrics are generated by our AI anomaly detection system\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              minWidth: '300px'\n            },\n            children: /*#__PURE__*/_jsxDEV(AIFeedbackButtons, {\n              suggestionId: \"analytics_summary\",\n              suggestionType: \"analytics_insights\",\n              onFeedback: handleFeedback,\n              variant: \"default\",\n              initialFeedback: feedbackStates['analytics_summary']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 4,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          lg: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Anomalies Timeline\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                mb: 2,\n                children: \"Anomaly detections over time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: 350,\n                children: /*#__PURE__*/_jsxDEV(LineChart, {\n                  data: summary,\n                  children: [/*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"formattedTime\",\n                    tick: {\n                      fontSize: 12\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                    tick: {\n                      fontSize: 12\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    labelFormatter: (label, payload) => {\n                      if (payload && payload[0]) {\n                        return format(new Date(payload[0].payload.time), 'MMM dd, yyyy HH:mm');\n                      }\n                      return label;\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Line, {\n                    type: \"monotone\",\n                    dataKey: \"count\",\n                    stroke: \"#1976d2\",\n                    strokeWidth: 2,\n                    dot: {\n                      fill: '#1976d2',\n                      strokeWidth: 2,\n                      r: 4\n                    },\n                    activeDot: {\n                      r: 6\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          lg: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Error Distribution\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                mb: 2,\n                children: \"Reconstruction error levels\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n                width: \"100%\",\n                height: 350,\n                children: /*#__PURE__*/_jsxDEV(BarChart, {\n                  data: errorDistribution,\n                  children: [/*#__PURE__*/_jsxDEV(XAxis, {\n                    dataKey: \"index\",\n                    tick: {\n                      fontSize: 10\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                    tick: {\n                      fontSize: 10\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    formatter: value => [value.toFixed(4), 'Error Rate']\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                    dataKey: \"value\",\n                    fill: \"#1976d2\",\n                    radius: [2, 2, 0, 0]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Security Anomaly Chart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1,\n                    p: 1,\n                    backgroundColor: 'background.paper',\n                    borderRadius: 1,\n                    border: '1px solid',\n                    borderColor: 'divider'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    fontWeight: 500,\n                    children: \"Was this chart helpful?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(AIFeedbackButtons, {\n                    suggestionId: \"anomaly_chart\",\n                    suggestionType: \"anomaly_visualization\",\n                    onFeedback: handleFeedback,\n                    variant: \"compact\",\n                    initialFeedback: feedbackStates['anomaly_chart']\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(AnomalyChart, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Activity Heatmap\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1,\n                    p: 1,\n                    backgroundColor: 'background.paper',\n                    borderRadius: 1,\n                    border: '1px solid',\n                    borderColor: 'divider'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    fontWeight: 500,\n                    children: \"Was this heatmap helpful?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(AIFeedbackButtons, {\n                    suggestionId: \"activity_heatmap\",\n                    suggestionType: \"heatmap_visualization\",\n                    onFeedback: handleFeedback,\n                    variant: \"compact\",\n                    initialFeedback: feedbackStates['activity_heatmap']\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(AnomalyHeatmap, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"analytics-section\",\n            sx: {\n              minHeight: '300px',\n              position: 'relative'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                minHeight: '250px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                justifyContent: \"space-between\",\n                alignItems: \"center\",\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Snapshot of Anomalies\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1,\n                    p: 1,\n                    backgroundColor: 'background.paper',\n                    borderRadius: 1,\n                    border: '1px solid',\n                    borderColor: 'divider'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    fontWeight: 500,\n                    children: \"Are these snapshots helpful?\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(AIFeedbackButtons, {\n                    suggestionId: \"anomaly_snapshots\",\n                    suggestionType: \"anomaly_snapshots\",\n                    onFeedback: handleFeedback,\n                    variant: \"compact\",\n                    initialFeedback: feedbackStates['anomaly_snapshots']\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(AnomalySnapshots, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 4,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              p: 3,\n              backgroundColor: 'info.light',\n              border: '2px solid',\n              borderColor: 'info.main'\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              flexDirection: \"column\",\n              gap: 2,\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: 40,\n                    height: 40,\n                    borderRadius: '50%',\n                    bgcolor: 'info.main',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '16px',\n                    fontWeight: 'bold'\n                  },\n                  children: \"AI\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  fontWeight: \"bold\",\n                  children: \"How was your analytics experience?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                color: \"text.secondary\",\n                textAlign: \"center\",\n                maxWidth: \"600px\",\n                children: \"Your feedback helps us improve our AI-powered analytics and anomaly detection system. Let us know if the insights and visualizations were helpful for your security monitoring needs.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(AIFeedbackButtons, {\n                suggestionId: \"overall_analytics_experience\",\n                suggestionType: \"analytics_experience\",\n                onFeedback: handleFeedback,\n                variant: \"default\",\n                initialFeedback: feedbackStates['overall_analytics_experience']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n}\n_s(Analytics, \"as9S4lP1pyUPcnBzTFOhEF587Ro=\", false, function () {\n  return [useFeedbackState];\n});\n_c = Analytics;\nvar _c;\n$RefreshReg$(_c, \"Analytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Box", "IconButton", "FormControl", "InputLabel", "Select", "MenuItem", "Chip", "Refresh", "TrendingUp", "Warning", "Assessment", "Timeline", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Bar", "format", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StatsCard", "Anoma<PERSON><PERSON><PERSON>", "AnomalyHeatmap", "AnomalySnapshots", "AIFeedbackButtons", "useFeedbackState", "jsxDEV", "_jsxDEV", "Analytics", "_s", "summary", "set<PERSON>ummary", "errors", "setErrors", "errorMsg", "setErrorMsg", "loading", "setLoading", "timeRange", "setTimeRange", "feedbackStates", "submitFeedback", "fetchData", "res1", "fetch", "ok", "Error", "status", "summaryJson", "json", "Array", "isArray", "summaryArr", "Object", "entries", "map", "time", "count", "formattedTime", "Date", "res2", "<PERSON><PERSON><PERSON>", "e", "console", "error", "totalAnomalies", "reduce", "sum", "item", "avgErrorRate", "length", "err", "toFixed", "maxAnomaliesPerMinute", "Math", "max", "errorDistribution", "index", "value", "parseFloat", "category", "handleFeedback", "suggestionId", "feedbackType", "suggestionType", "analytics_data", "total_anomalies", "avg_error_rate", "time_range", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "sx", "py", "className", "children", "mb", "variant", "component", "gutterBottom", "fontWeight", "color", "display", "gap", "flexWrap", "alignItems", "size", "min<PERSON><PERSON><PERSON>", "label", "onChange", "target", "onClick", "disabled", "icon", "onRetry", "container", "spacing", "xs", "sm", "md", "title", "subtitle", "p", "backgroundColor", "border", "borderColor", "transition", "justifyContent", "fontSize", "onFeedback", "initialFeedback", "lg", "width", "height", "data", "dataKey", "tick", "labelFormatter", "payload", "type", "stroke", "strokeWidth", "dot", "fill", "r", "activeDot", "formatter", "radius", "borderRadius", "minHeight", "position", "flexDirection", "bgcolor", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/capstone final project/SafeRoomAI-Athira/frontend/src/screens/Analytics.jsx"], "sourcesContent": ["// src/screens/Analytics.jsx\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport {\r\n  Container,\r\n  Typography,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  Box,\r\n  IconButton,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  MenuItem,\r\n  Chip\r\n} from '@mui/material';\r\nimport {\r\n  Refresh,\r\n  TrendingUp,\r\n  Warning,\r\n  Assessment,\r\n  Timeline\r\n} from '@mui/icons-material';\r\nimport {\r\n  LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer,\r\n  BarChart, Bar\r\n} from 'recharts';\r\nimport '../App.css';\r\nimport { format } from 'date-fns';\r\nimport LoadingSpinner from '../components/LoadingSpinner';\r\nimport ErrorAlert from '../components/ErrorAlert';\r\nimport StatsCard from '../components/StatsCard';\r\nimport AnomalyChart from '../components/anomalychart';\r\nimport AnomalyHeatmap from '../components/anomalyheatmap';\r\nimport AnomalySnapshots from '../components/anomalysnapshots';\r\nimport AIFeedbackButtons from '../components/AIFeedbackButtons';\r\nimport useFeedbackState from '../hooks/useFeedbackState';\r\n\r\nexport default function Analytics() {\r\n  const [summary, setSummary] = useState([]);\r\n  const [errors, setErrors] = useState([]);\r\n  const [errorMsg, setErrorMsg] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [timeRange, setTimeRange] = useState('24h');\r\n\r\n\r\n\r\n  // Use the feedback state hook\r\n  const { feedbackStates, submitFeedback } = useFeedbackState('analytics');\r\n\r\n  useEffect(() => {\r\n    fetchData();\r\n  }, []);\r\n\r\n  const fetchData = async () => {\r\n    try {\r\n      setLoading(true);\r\n      // Fetch anomalies‐per‐minute summary\r\n      const res1 = await fetch('/predict/analytics/summary');\r\n      if (!res1.ok) throw new Error(`Summary HTTP ${res1.status}`);\r\n      const summaryJson = await res1.json();\r\n      if (summaryJson && typeof summaryJson === 'object' && !Array.isArray(summaryJson)) {\r\n        const summaryArr = Object.entries(summaryJson).map(([time, count]) => ({\r\n          time,\r\n          count,\r\n          formattedTime: format(new Date(time), 'HH:mm')\r\n        }));\r\n        setSummary(summaryArr);\r\n      } else {\r\n        setSummary([]);\r\n      }\r\n\r\n      // Fetch recent reconstruction errors\r\n      const res2 = await fetch('/predict/analytics/errors');\r\n      if (!res2.ok) throw new Error(`Errors HTTP ${res2.status}`);\r\n      const errorsJson = await res2.json();\r\n      if (Array.isArray(errorsJson)) {\r\n        setErrors(errorsJson);\r\n      } else {\r\n        setErrors([]);\r\n      }\r\n\r\n      setErrorMsg(null);\r\n    } catch (e) {\r\n      console.error('Analytics fetch error:', e);\r\n      setErrorMsg('Failed to load analytics data.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Calculate statistics\r\n  const totalAnomalies = summary.reduce((sum, item) => sum + item.count, 0);\r\n  const avgErrorRate = errors.length > 0 ? (errors.reduce((sum, err) => sum + err, 0) / errors.length).toFixed(4) : 0;\r\n  const maxAnomaliesPerMinute = summary.length > 0 ? Math.max(...summary.map(item => item.count)) : 0;\r\n\r\n  // Prepare error distribution data\r\n  const errorDistribution = errors.map((error, index) => ({\r\n    index: index + 1,\r\n    value: parseFloat(error.toFixed(4)),\r\n    category: error > 0.5 ? 'High' : error > 0.2 ? 'Medium' : 'Low'\r\n  }));\r\n\r\n  const handleFeedback = async (suggestionId, feedbackType, suggestionType) => {\r\n    await submitFeedback(suggestionId, feedbackType, suggestionType, {\r\n      analytics_data: {\r\n        total_anomalies: totalAnomalies,\r\n        avg_error_rate: avgErrorRate,\r\n        time_range: timeRange\r\n      }\r\n    });\r\n  };\r\n\r\n\r\n\r\n  if (loading) {\r\n    return <LoadingSpinner message=\"Loading analytics data...\" />;\r\n  }\r\n\r\n  return (\r\n    <Container maxWidth=\"xl\" sx={{ py: 2 }} className=\"analytics-container\">\r\n      <Box mb={4} className=\"analytics-section\">\r\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom fontWeight=\"bold\">\r\n          Analytics Dashboard\r\n        </Typography>\r\n        <Typography variant=\"body1\" color=\"text.secondary\">\r\n          Real-time insights and anomaly detection metrics\r\n        </Typography>\r\n      </Box>\r\n\r\n      {/* Controls */}\r\n      <Box display=\"flex\" gap={2} mb={3} flexWrap=\"wrap\" alignItems=\"center\">\r\n        <FormControl size=\"small\" sx={{ minWidth: 120 }}>\r\n          <InputLabel>Time Range</InputLabel>\r\n          <Select\r\n            value={timeRange}\r\n            label=\"Time Range\"\r\n            onChange={(e) => setTimeRange(e.target.value)}\r\n          >\r\n            <MenuItem value=\"1h\">Last Hour</MenuItem>\r\n            <MenuItem value=\"24h\">Last 24 Hours</MenuItem>\r\n            <MenuItem value=\"7d\">Last 7 Days</MenuItem>\r\n          </Select>\r\n        </FormControl>\r\n        <IconButton onClick={fetchData} disabled={loading}>\r\n          <Refresh />\r\n        </IconButton>\r\n        <Chip\r\n          icon={<Assessment />}\r\n          label={`${summary.length} data points`}\r\n          color=\"primary\"\r\n          variant=\"outlined\"\r\n        />\r\n      </Box>\r\n\r\n      {errorMsg && (\r\n        <ErrorAlert\r\n          message={errorMsg}\r\n          onRetry={fetchData}\r\n          sx={{ mb: 3 }}\r\n        />\r\n      )}\r\n\r\n      {/* Statistics Cards */}\r\n      <Box mb={4}>\r\n        <Grid container spacing={3}>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <StatsCard\r\n            title=\"Total Anomalies\"\r\n            value={totalAnomalies}\r\n            subtitle=\"Detected events\"\r\n            icon={<Warning />}\r\n            color=\"warning\"\r\n          />\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <StatsCard\r\n            title=\"Avg Error Rate\"\r\n            value={avgErrorRate}\r\n            subtitle=\"Reconstruction error\"\r\n            icon={<TrendingUp />}\r\n            color=\"error\"\r\n          />\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <StatsCard\r\n            title=\"Peak Activity\"\r\n            value={maxAnomaliesPerMinute}\r\n            subtitle=\"Max per minute\"\r\n            icon={<Timeline />}\r\n            color=\"info\"\r\n          />\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <StatsCard\r\n            title=\"Data Points\"\r\n            value={errors.length}\r\n            subtitle=\"Error samples\"\r\n            icon={<Assessment />}\r\n            color=\"primary\"\r\n          />\r\n        </Grid>\r\n        </Grid>\r\n      </Box>\r\n\r\n      {/* AI Analytics Feedback Section */}\r\n      <Box mb={3}>\r\n        <Card\r\n          sx={{\r\n            p: 2,\r\n            backgroundColor: 'background.default',\r\n            border: feedbackStates['analytics_summary'] === 'accept'\r\n              ? '2px solid #10b981'\r\n              : feedbackStates['analytics_summary'] === 'reject'\r\n              ? '2px solid #ef4444'\r\n              : '1px solid',\r\n            borderColor: feedbackStates['analytics_summary'] ? 'transparent' : 'divider',\r\n            transition: 'all 0.3s ease'\r\n          }}\r\n        >\r\n          <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\" gap={2}>\r\n            <Box>\r\n              <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                  AI Analytics Insights\r\n                </Typography>\r\n                {feedbackStates['analytics_summary'] && (\r\n                  <Chip\r\n                    size=\"small\"\r\n                    label={feedbackStates['analytics_summary'] === 'accept' ? 'Helpful' : 'Needs improvement'}\r\n                    color={feedbackStates['analytics_summary'] === 'accept' ? 'success' : 'warning'}\r\n                    sx={{ fontSize: '0.7rem' }}\r\n                  />\r\n                )}\r\n              </Box>\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                These metrics are generated by our AI anomaly detection system\r\n              </Typography>\r\n            </Box>\r\n            <Box sx={{ minWidth: '300px' }}>\r\n              <AIFeedbackButtons\r\n                suggestionId=\"analytics_summary\"\r\n                suggestionType=\"analytics_insights\"\r\n                onFeedback={handleFeedback}\r\n                variant=\"default\"\r\n                initialFeedback={feedbackStates['analytics_summary']}\r\n              />\r\n            </Box>\r\n          </Box>\r\n        </Card>\r\n      </Box>\r\n\r\n      {/* Real-time Charts */}\r\n      <Box mb={4}>\r\n        <Grid container spacing={3}>\r\n        {/* Anomalies Timeline Chart */}\r\n        <Grid item xs={12} lg={8}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Anomalies Timeline\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\" mb={2}>\r\n                Anomaly detections over time\r\n              </Typography>\r\n              <ResponsiveContainer width=\"100%\" height={350}>\r\n                <LineChart data={summary}>\r\n                  <XAxis\r\n                    dataKey=\"formattedTime\"\r\n                    tick={{ fontSize: 12 }}\r\n                  />\r\n                  <YAxis tick={{ fontSize: 12 }} />\r\n                  <Tooltip\r\n                    labelFormatter={(label, payload) => {\r\n                      if (payload && payload[0]) {\r\n                        return format(new Date(payload[0].payload.time), 'MMM dd, yyyy HH:mm');\r\n                      }\r\n                      return label;\r\n                    }}\r\n                  />\r\n                  <Line\r\n                    type=\"monotone\"\r\n                    dataKey=\"count\"\r\n                    stroke=\"#1976d2\"\r\n                    strokeWidth={2}\r\n                    dot={{ fill: '#1976d2', strokeWidth: 2, r: 4 }}\r\n                    activeDot={{ r: 6 }}\r\n                  />\r\n                </LineChart>\r\n              </ResponsiveContainer>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Error Distribution Chart */}\r\n        <Grid item xs={12} lg={4}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Error Distribution\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\" mb={2}>\r\n                Reconstruction error levels\r\n              </Typography>\r\n              <ResponsiveContainer width=\"100%\" height={350}>\r\n                <BarChart data={errorDistribution}>\r\n                  <XAxis dataKey=\"index\" tick={{ fontSize: 10 }} />\r\n                  <YAxis tick={{ fontSize: 10 }} />\r\n                  <Tooltip\r\n                    formatter={(value) => [value.toFixed(4), 'Error Rate']}\r\n                  />\r\n                  <Bar\r\n                    dataKey=\"value\"\r\n                    fill=\"#1976d2\"\r\n                    radius={[2, 2, 0, 0]}\r\n                  />\r\n                </BarChart>\r\n              </ResponsiveContainer>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Real-time Anomaly Charts - Your Custom Components */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\r\n                <Typography variant=\"h6\">\r\n                  Security Anomaly Chart\r\n                </Typography>\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  gap: 1,\r\n                  p: 1,\r\n                  backgroundColor: 'background.paper',\r\n                  borderRadius: 1,\r\n                  border: '1px solid',\r\n                  borderColor: 'divider'\r\n                }}>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\" fontWeight={500}>\r\n                    Was this chart helpful?\r\n                  </Typography>\r\n                  <AIFeedbackButtons\r\n                    suggestionId=\"anomaly_chart\"\r\n                    suggestionType=\"anomaly_visualization\"\r\n                    onFeedback={handleFeedback}\r\n                    variant=\"compact\"\r\n                    initialFeedback={feedbackStates['anomaly_chart']}\r\n                  />\r\n                </Box>\r\n              </Box>\r\n              <AnomalyChart />\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\r\n                <Typography variant=\"h6\">\r\n                  Activity Heatmap\r\n                </Typography>\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  gap: 1,\r\n                  p: 1,\r\n                  backgroundColor: 'background.paper',\r\n                  borderRadius: 1,\r\n                  border: '1px solid',\r\n                  borderColor: 'divider'\r\n                }}>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\" fontWeight={500}>\r\n                    Was this heatmap helpful?\r\n                  </Typography>\r\n                  <AIFeedbackButtons\r\n                    suggestionId=\"activity_heatmap\"\r\n                    suggestionType=\"heatmap_visualization\"\r\n                    onFeedback={handleFeedback}\r\n                    variant=\"compact\"\r\n                    initialFeedback={feedbackStates['activity_heatmap']}\r\n                  />\r\n                </Box>\r\n              </Box>\r\n              <AnomalyHeatmap />\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        <Grid item xs={12}>\r\n          <Card className=\"analytics-section\" sx={{ minHeight: '300px', position: 'relative' }}>\r\n            <CardContent sx={{ minHeight: '250px' }}>\r\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                  Snapshot of Anomalies\r\n                </Typography>\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  gap: 1,\r\n                  p: 1,\r\n                  backgroundColor: 'background.paper',\r\n                  borderRadius: 1,\r\n                  border: '1px solid',\r\n                  borderColor: 'divider'\r\n                }}>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\" fontWeight={500}>\r\n                    Are these snapshots helpful?\r\n                  </Typography>\r\n                  <AIFeedbackButtons\r\n                    suggestionId=\"anomaly_snapshots\"\r\n                    suggestionType=\"anomaly_snapshots\"\r\n                    onFeedback={handleFeedback}\r\n                    variant=\"compact\"\r\n                    initialFeedback={feedbackStates['anomaly_snapshots']}\r\n                  />\r\n                </Box>\r\n              </Box>\r\n              <AnomalySnapshots />\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n        </Grid>\r\n      </Box>\r\n\r\n      {/* Overall Analytics Feedback Section */}\r\n      <Box mb={4}>\r\n        <Grid container spacing={3}>\r\n          <Grid item xs={12}>\r\n          <Card\r\n            sx={{\r\n              p: 3,\r\n              backgroundColor: 'info.light',\r\n              border: '2px solid',\r\n              borderColor: 'info.main'\r\n            }}\r\n          >\r\n            <Box display=\"flex\" alignItems=\"center\" justifyContent=\"center\" flexDirection=\"column\" gap={2}>\r\n              <Box display=\"flex\" alignItems=\"center\" gap={2}>\r\n                <Box\r\n                  sx={{\r\n                    width: 40,\r\n                    height: 40,\r\n                    borderRadius: '50%',\r\n                    bgcolor: 'info.main',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center',\r\n                    color: 'white',\r\n                    fontSize: '16px',\r\n                    fontWeight: 'bold'\r\n                  }}\r\n                >\r\n                  AI\r\n                </Box>\r\n                <Typography variant=\"h5\" fontWeight=\"bold\">\r\n                  How was your analytics experience?\r\n                </Typography>\r\n              </Box>\r\n\r\n              <Typography variant=\"body1\" color=\"text.secondary\" textAlign=\"center\" maxWidth=\"600px\">\r\n                Your feedback helps us improve our AI-powered analytics and anomaly detection system.\r\n                Let us know if the insights and visualizations were helpful for your security monitoring needs.\r\n              </Typography>\r\n\r\n              <AIFeedbackButtons\r\n                suggestionId=\"overall_analytics_experience\"\r\n                suggestionType=\"analytics_experience\"\r\n                onFeedback={handleFeedback}\r\n                variant=\"default\"\r\n                initialFeedback={feedbackStates['overall_analytics_experience']}\r\n              />\r\n            </Box>\r\n          </Card>\r\n          </Grid>\r\n        </Grid>\r\n      </Box>\r\n    </Container>\r\n  );\r\n}\r\n\r\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,GAAG,EACHC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,QACC,eAAe;AACtB,SACEC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,QAAQ,QACH,qBAAqB;AAC5B,SACEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,mBAAmB,EAC3DC,QAAQ,EAAEC,GAAG,QACR,UAAU;AACjB,OAAO,YAAY;AACnB,SAASC,MAAM,QAAQ,UAAU;AACjC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,gBAAgB,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,eAAe,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAClC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0C,MAAM,EAAEC,SAAS,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;;EAIjD;EACA,MAAM;IAAEkD,cAAc;IAAEC;EAAe,CAAC,GAAGhB,gBAAgB,CAAC,WAAW,CAAC;EAExElC,SAAS,CAAC,MAAM;IACdmD,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMM,IAAI,GAAG,MAAMC,KAAK,CAAC,4BAA4B,CAAC;MACtD,IAAI,CAACD,IAAI,CAACE,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,gBAAgBH,IAAI,CAACI,MAAM,EAAE,CAAC;MAC5D,MAAMC,WAAW,GAAG,MAAML,IAAI,CAACM,IAAI,CAAC,CAAC;MACrC,IAAID,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,EAAE;QACjF,MAAMI,UAAU,GAAGC,MAAM,CAACC,OAAO,CAACN,WAAW,CAAC,CAACO,GAAG,CAAC,CAAC,CAACC,IAAI,EAAEC,KAAK,CAAC,MAAM;UACrED,IAAI;UACJC,KAAK;UACLC,aAAa,EAAEzC,MAAM,CAAC,IAAI0C,IAAI,CAACH,IAAI,CAAC,EAAE,OAAO;QAC/C,CAAC,CAAC,CAAC;QACHzB,UAAU,CAACqB,UAAU,CAAC;MACxB,CAAC,MAAM;QACLrB,UAAU,CAAC,EAAE,CAAC;MAChB;;MAEA;MACA,MAAM6B,IAAI,GAAG,MAAMhB,KAAK,CAAC,2BAA2B,CAAC;MACrD,IAAI,CAACgB,IAAI,CAACf,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,eAAec,IAAI,CAACb,MAAM,EAAE,CAAC;MAC3D,MAAMc,UAAU,GAAG,MAAMD,IAAI,CAACX,IAAI,CAAC,CAAC;MACpC,IAAIC,KAAK,CAACC,OAAO,CAACU,UAAU,CAAC,EAAE;QAC7B5B,SAAS,CAAC4B,UAAU,CAAC;MACvB,CAAC,MAAM;QACL5B,SAAS,CAAC,EAAE,CAAC;MACf;MAEAE,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC,CAAC,OAAO2B,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEF,CAAC,CAAC;MAC1C3B,WAAW,CAAC,gCAAgC,CAAC;IAC/C,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4B,cAAc,GAAGnC,OAAO,CAACoC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACX,KAAK,EAAE,CAAC,CAAC;EACzE,MAAMY,YAAY,GAAGrC,MAAM,CAACsC,MAAM,GAAG,CAAC,GAAG,CAACtC,MAAM,CAACkC,MAAM,CAAC,CAACC,GAAG,EAAEI,GAAG,KAAKJ,GAAG,GAAGI,GAAG,EAAE,CAAC,CAAC,GAAGvC,MAAM,CAACsC,MAAM,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EACnH,MAAMC,qBAAqB,GAAG3C,OAAO,CAACwC,MAAM,GAAG,CAAC,GAAGI,IAAI,CAACC,GAAG,CAAC,GAAG7C,OAAO,CAACyB,GAAG,CAACa,IAAI,IAAIA,IAAI,CAACX,KAAK,CAAC,CAAC,GAAG,CAAC;;EAEnG;EACA,MAAMmB,iBAAiB,GAAG5C,MAAM,CAACuB,GAAG,CAAC,CAACS,KAAK,EAAEa,KAAK,MAAM;IACtDA,KAAK,EAAEA,KAAK,GAAG,CAAC;IAChBC,KAAK,EAAEC,UAAU,CAACf,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC;IACnCQ,QAAQ,EAAEhB,KAAK,GAAG,GAAG,GAAG,MAAM,GAAGA,KAAK,GAAG,GAAG,GAAG,QAAQ,GAAG;EAC5D,CAAC,CAAC,CAAC;EAEH,MAAMiB,cAAc,GAAG,MAAAA,CAAOC,YAAY,EAAEC,YAAY,EAAEC,cAAc,KAAK;IAC3E,MAAM3C,cAAc,CAACyC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAE;MAC/DC,cAAc,EAAE;QACdC,eAAe,EAAErB,cAAc;QAC/BsB,cAAc,EAAElB,YAAY;QAC5BmB,UAAU,EAAElD;MACd;IACF,CAAC,CAAC;EACJ,CAAC;EAID,IAAIF,OAAO,EAAE;IACX,oBAAOT,OAAA,CAACT,cAAc;MAACuE,OAAO,EAAC;IAA2B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC/D;EAEA,oBACElE,OAAA,CAACnC,SAAS;IAACsG,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAACC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBACrEvE,OAAA,CAAC9B,GAAG;MAACsG,EAAE,EAAE,CAAE;MAACF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBACvCvE,OAAA,CAAClC,UAAU;QAAC2G,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAACC,UAAU,EAAC,MAAM;QAAAL,QAAA,EAAC;MAEvE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACblE,OAAA,CAAClC,UAAU;QAAC2G,OAAO,EAAC,OAAO;QAACI,KAAK,EAAC,gBAAgB;QAAAN,QAAA,EAAC;MAEnD;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNlE,OAAA,CAAC9B,GAAG;MAAC4G,OAAO,EAAC,MAAM;MAACC,GAAG,EAAE,CAAE;MAACP,EAAE,EAAE,CAAE;MAACQ,QAAQ,EAAC,MAAM;MAACC,UAAU,EAAC,QAAQ;MAAAV,QAAA,gBACpEvE,OAAA,CAAC5B,WAAW;QAAC8G,IAAI,EAAC,OAAO;QAACd,EAAE,EAAE;UAAEe,QAAQ,EAAE;QAAI,CAAE;QAAAZ,QAAA,gBAC9CvE,OAAA,CAAC3B,UAAU;UAAAkG,QAAA,EAAC;QAAU;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACnClE,OAAA,CAAC1B,MAAM;UACL6E,KAAK,EAAExC,SAAU;UACjByE,KAAK,EAAC,YAAY;UAClBC,QAAQ,EAAGlD,CAAC,IAAKvB,YAAY,CAACuB,CAAC,CAACmD,MAAM,CAACnC,KAAK,CAAE;UAAAoB,QAAA,gBAE9CvE,OAAA,CAACzB,QAAQ;YAAC4E,KAAK,EAAC,IAAI;YAAAoB,QAAA,EAAC;UAAS;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACzClE,OAAA,CAACzB,QAAQ;YAAC4E,KAAK,EAAC,KAAK;YAAAoB,QAAA,EAAC;UAAa;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC9ClE,OAAA,CAACzB,QAAQ;YAAC4E,KAAK,EAAC,IAAI;YAAAoB,QAAA,EAAC;UAAW;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACdlE,OAAA,CAAC7B,UAAU;QAACoH,OAAO,EAAExE,SAAU;QAACyE,QAAQ,EAAE/E,OAAQ;QAAA8D,QAAA,eAChDvE,OAAA,CAACvB,OAAO;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACblE,OAAA,CAACxB,IAAI;QACHiH,IAAI,eAAEzF,OAAA,CAACpB,UAAU;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACrBkB,KAAK,EAAE,GAAGjF,OAAO,CAACwC,MAAM,cAAe;QACvCkC,KAAK,EAAC,SAAS;QACfJ,OAAO,EAAC;MAAU;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEL3D,QAAQ,iBACPP,OAAA,CAACR,UAAU;MACTsE,OAAO,EAAEvD,QAAS;MAClBmF,OAAO,EAAE3E,SAAU;MACnBqD,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE;IAAE;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CACF,eAGDlE,OAAA,CAAC9B,GAAG;MAACsG,EAAE,EAAE,CAAE;MAAAD,QAAA,eACTvE,OAAA,CAACjC,IAAI;QAAC4H,SAAS;QAACC,OAAO,EAAE,CAAE;QAAArB,QAAA,gBAC3BvE,OAAA,CAACjC,IAAI;UAAC0E,IAAI;UAACoD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAxB,QAAA,eAC9BvE,OAAA,CAACP,SAAS;YACRuG,KAAK,EAAC,iBAAiB;YACvB7C,KAAK,EAAEb,cAAe;YACtB2D,QAAQ,EAAC,iBAAiB;YAC1BR,IAAI,eAAEzF,OAAA,CAACrB,OAAO;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAClBW,KAAK,EAAC;UAAS;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPlE,OAAA,CAACjC,IAAI;UAAC0E,IAAI;UAACoD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAxB,QAAA,eAC9BvE,OAAA,CAACP,SAAS;YACRuG,KAAK,EAAC,gBAAgB;YACtB7C,KAAK,EAAET,YAAa;YACpBuD,QAAQ,EAAC,sBAAsB;YAC/BR,IAAI,eAAEzF,OAAA,CAACtB,UAAU;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACrBW,KAAK,EAAC;UAAO;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPlE,OAAA,CAACjC,IAAI;UAAC0E,IAAI;UAACoD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAxB,QAAA,eAC9BvE,OAAA,CAACP,SAAS;YACRuG,KAAK,EAAC,eAAe;YACrB7C,KAAK,EAAEL,qBAAsB;YAC7BmD,QAAQ,EAAC,gBAAgB;YACzBR,IAAI,eAAEzF,OAAA,CAACnB,QAAQ;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBW,KAAK,EAAC;UAAM;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPlE,OAAA,CAACjC,IAAI;UAAC0E,IAAI;UAACoD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAxB,QAAA,eAC9BvE,OAAA,CAACP,SAAS;YACRuG,KAAK,EAAC,aAAa;YACnB7C,KAAK,EAAE9C,MAAM,CAACsC,MAAO;YACrBsD,QAAQ,EAAC,eAAe;YACxBR,IAAI,eAAEzF,OAAA,CAACpB,UAAU;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACrBW,KAAK,EAAC;UAAS;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNlE,OAAA,CAAC9B,GAAG;MAACsG,EAAE,EAAE,CAAE;MAAAD,QAAA,eACTvE,OAAA,CAAChC,IAAI;QACHoG,EAAE,EAAE;UACF8B,CAAC,EAAE,CAAC;UACJC,eAAe,EAAE,oBAAoB;UACrCC,MAAM,EAAEvF,cAAc,CAAC,mBAAmB,CAAC,KAAK,QAAQ,GACpD,mBAAmB,GACnBA,cAAc,CAAC,mBAAmB,CAAC,KAAK,QAAQ,GAChD,mBAAmB,GACnB,WAAW;UACfwF,WAAW,EAAExF,cAAc,CAAC,mBAAmB,CAAC,GAAG,aAAa,GAAG,SAAS;UAC5EyF,UAAU,EAAE;QACd,CAAE;QAAA/B,QAAA,eAEFvE,OAAA,CAAC9B,GAAG;UAAC4G,OAAO,EAAC,MAAM;UAACG,UAAU,EAAC,QAAQ;UAACsB,cAAc,EAAC,eAAe;UAACvB,QAAQ,EAAC,MAAM;UAACD,GAAG,EAAE,CAAE;UAAAR,QAAA,gBAC5FvE,OAAA,CAAC9B,GAAG;YAAAqG,QAAA,gBACFvE,OAAA,CAAC9B,GAAG;cAAC4G,OAAO,EAAC,MAAM;cAACG,UAAU,EAAC,QAAQ;cAACF,GAAG,EAAE,CAAE;cAAAR,QAAA,gBAC7CvE,OAAA,CAAClC,UAAU;gBAAC2G,OAAO,EAAC,IAAI;gBAACE,YAAY;gBAAAJ,QAAA,EAAC;cAEtC;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZrD,cAAc,CAAC,mBAAmB,CAAC,iBAClCb,OAAA,CAACxB,IAAI;gBACH0G,IAAI,EAAC,OAAO;gBACZE,KAAK,EAAEvE,cAAc,CAAC,mBAAmB,CAAC,KAAK,QAAQ,GAAG,SAAS,GAAG,mBAAoB;gBAC1FgE,KAAK,EAAEhE,cAAc,CAAC,mBAAmB,CAAC,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;gBAChFuD,EAAE,EAAE;kBAAEoC,QAAQ,EAAE;gBAAS;cAAE;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNlE,OAAA,CAAClC,UAAU;cAAC2G,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAN,QAAA,EAAC;YAEnD;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNlE,OAAA,CAAC9B,GAAG;YAACkG,EAAE,EAAE;cAAEe,QAAQ,EAAE;YAAQ,CAAE;YAAAZ,QAAA,eAC7BvE,OAAA,CAACH,iBAAiB;cAChB0D,YAAY,EAAC,mBAAmB;cAChCE,cAAc,EAAC,oBAAoB;cACnCgD,UAAU,EAAEnD,cAAe;cAC3BmB,OAAO,EAAC,SAAS;cACjBiC,eAAe,EAAE7F,cAAc,CAAC,mBAAmB;YAAE;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNlE,OAAA,CAAC9B,GAAG;MAACsG,EAAE,EAAE,CAAE;MAAAD,QAAA,eACTvE,OAAA,CAACjC,IAAI;QAAC4H,SAAS;QAACC,OAAO,EAAE,CAAE;QAAArB,QAAA,gBAE3BvE,OAAA,CAACjC,IAAI;UAAC0E,IAAI;UAACoD,EAAE,EAAE,EAAG;UAACc,EAAE,EAAE,CAAE;UAAApC,QAAA,eACvBvE,OAAA,CAAChC,IAAI;YAAAuG,QAAA,eACHvE,OAAA,CAAC/B,WAAW;cAAAsG,QAAA,gBACVvE,OAAA,CAAClC,UAAU;gBAAC2G,OAAO,EAAC,IAAI;gBAACE,YAAY;gBAAAJ,QAAA,EAAC;cAEtC;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblE,OAAA,CAAClC,UAAU;gBAAC2G,OAAO,EAAC,OAAO;gBAACI,KAAK,EAAC,gBAAgB;gBAACL,EAAE,EAAE,CAAE;gBAAAD,QAAA,EAAC;cAE1D;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblE,OAAA,CAACb,mBAAmB;gBAACyH,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAE,GAAI;gBAAAtC,QAAA,eAC5CvE,OAAA,CAAClB,SAAS;kBAACgI,IAAI,EAAE3G,OAAQ;kBAAAoE,QAAA,gBACvBvE,OAAA,CAAChB,KAAK;oBACJ+H,OAAO,EAAC,eAAe;oBACvBC,IAAI,EAAE;sBAAER,QAAQ,EAAE;oBAAG;kBAAE;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACFlE,OAAA,CAACf,KAAK;oBAAC+H,IAAI,EAAE;sBAAER,QAAQ,EAAE;oBAAG;kBAAE;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjClE,OAAA,CAACd,OAAO;oBACN+H,cAAc,EAAEA,CAAC7B,KAAK,EAAE8B,OAAO,KAAK;sBAClC,IAAIA,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,EAAE;wBACzB,OAAO5H,MAAM,CAAC,IAAI0C,IAAI,CAACkF,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAACrF,IAAI,CAAC,EAAE,oBAAoB,CAAC;sBACxE;sBACA,OAAOuD,KAAK;oBACd;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFlE,OAAA,CAACjB,IAAI;oBACHoI,IAAI,EAAC,UAAU;oBACfJ,OAAO,EAAC,OAAO;oBACfK,MAAM,EAAC,SAAS;oBAChBC,WAAW,EAAE,CAAE;oBACfC,GAAG,EAAE;sBAAEC,IAAI,EAAE,SAAS;sBAAEF,WAAW,EAAE,CAAC;sBAAEG,CAAC,EAAE;oBAAE,CAAE;oBAC/CC,SAAS,EAAE;sBAAED,CAAC,EAAE;oBAAE;kBAAE;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPlE,OAAA,CAACjC,IAAI;UAAC0E,IAAI;UAACoD,EAAE,EAAE,EAAG;UAACc,EAAE,EAAE,CAAE;UAAApC,QAAA,eACvBvE,OAAA,CAAChC,IAAI;YAAAuG,QAAA,eACHvE,OAAA,CAAC/B,WAAW;cAAAsG,QAAA,gBACVvE,OAAA,CAAClC,UAAU;gBAAC2G,OAAO,EAAC,IAAI;gBAACE,YAAY;gBAAAJ,QAAA,EAAC;cAEtC;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblE,OAAA,CAAClC,UAAU;gBAAC2G,OAAO,EAAC,OAAO;gBAACI,KAAK,EAAC,gBAAgB;gBAACL,EAAE,EAAE,CAAE;gBAAAD,QAAA,EAAC;cAE1D;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblE,OAAA,CAACb,mBAAmB;gBAACyH,KAAK,EAAC,MAAM;gBAACC,MAAM,EAAE,GAAI;gBAAAtC,QAAA,eAC5CvE,OAAA,CAACZ,QAAQ;kBAAC0H,IAAI,EAAE7D,iBAAkB;kBAAAsB,QAAA,gBAChCvE,OAAA,CAAChB,KAAK;oBAAC+H,OAAO,EAAC,OAAO;oBAACC,IAAI,EAAE;sBAAER,QAAQ,EAAE;oBAAG;kBAAE;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjDlE,OAAA,CAACf,KAAK;oBAAC+H,IAAI,EAAE;sBAAER,QAAQ,EAAE;oBAAG;kBAAE;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjClE,OAAA,CAACd,OAAO;oBACNwI,SAAS,EAAGvE,KAAK,IAAK,CAACA,KAAK,CAACN,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY;kBAAE;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,eACFlE,OAAA,CAACX,GAAG;oBACF0H,OAAO,EAAC,OAAO;oBACfQ,IAAI,EAAC,SAAS;oBACdI,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;kBAAE;oBAAA5D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPlE,OAAA,CAACjC,IAAI;UAAC0E,IAAI;UAACoD,EAAE,EAAE,EAAG;UAACc,EAAE,EAAE,CAAE;UAAApC,QAAA,eACvBvE,OAAA,CAAChC,IAAI;YAAAuG,QAAA,eACHvE,OAAA,CAAC/B,WAAW;cAAAsG,QAAA,gBACVvE,OAAA,CAAC9B,GAAG;gBAAC4G,OAAO,EAAC,MAAM;gBAACyB,cAAc,EAAC,eAAe;gBAACtB,UAAU,EAAC,QAAQ;gBAACT,EAAE,EAAE,CAAE;gBAAAD,QAAA,gBAC3EvE,OAAA,CAAClC,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAAAF,QAAA,EAAC;gBAEzB;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACblE,OAAA,CAAC9B,GAAG;kBAACkG,EAAE,EAAE;oBACPU,OAAO,EAAE,MAAM;oBACfG,UAAU,EAAE,QAAQ;oBACpBF,GAAG,EAAE,CAAC;oBACNmB,CAAC,EAAE,CAAC;oBACJC,eAAe,EAAE,kBAAkB;oBACnCyB,YAAY,EAAE,CAAC;oBACfxB,MAAM,EAAE,WAAW;oBACnBC,WAAW,EAAE;kBACf,CAAE;kBAAA9B,QAAA,gBACAvE,OAAA,CAAClC,UAAU;oBAAC2G,OAAO,EAAC,OAAO;oBAACI,KAAK,EAAC,gBAAgB;oBAACD,UAAU,EAAE,GAAI;oBAAAL,QAAA,EAAC;kBAEpE;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACblE,OAAA,CAACH,iBAAiB;oBAChB0D,YAAY,EAAC,eAAe;oBAC5BE,cAAc,EAAC,uBAAuB;oBACtCgD,UAAU,EAAEnD,cAAe;oBAC3BmB,OAAO,EAAC,SAAS;oBACjBiC,eAAe,EAAE7F,cAAc,CAAC,eAAe;kBAAE;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlE,OAAA,CAACN,YAAY;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPlE,OAAA,CAACjC,IAAI;UAAC0E,IAAI;UAACoD,EAAE,EAAE,EAAG;UAACc,EAAE,EAAE,CAAE;UAAApC,QAAA,eACvBvE,OAAA,CAAChC,IAAI;YAAAuG,QAAA,eACHvE,OAAA,CAAC/B,WAAW;cAAAsG,QAAA,gBACVvE,OAAA,CAAC9B,GAAG;gBAAC4G,OAAO,EAAC,MAAM;gBAACyB,cAAc,EAAC,eAAe;gBAACtB,UAAU,EAAC,QAAQ;gBAACT,EAAE,EAAE,CAAE;gBAAAD,QAAA,gBAC3EvE,OAAA,CAAClC,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAAAF,QAAA,EAAC;gBAEzB;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACblE,OAAA,CAAC9B,GAAG;kBAACkG,EAAE,EAAE;oBACPU,OAAO,EAAE,MAAM;oBACfG,UAAU,EAAE,QAAQ;oBACpBF,GAAG,EAAE,CAAC;oBACNmB,CAAC,EAAE,CAAC;oBACJC,eAAe,EAAE,kBAAkB;oBACnCyB,YAAY,EAAE,CAAC;oBACfxB,MAAM,EAAE,WAAW;oBACnBC,WAAW,EAAE;kBACf,CAAE;kBAAA9B,QAAA,gBACAvE,OAAA,CAAClC,UAAU;oBAAC2G,OAAO,EAAC,OAAO;oBAACI,KAAK,EAAC,gBAAgB;oBAACD,UAAU,EAAE,GAAI;oBAAAL,QAAA,EAAC;kBAEpE;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACblE,OAAA,CAACH,iBAAiB;oBAChB0D,YAAY,EAAC,kBAAkB;oBAC/BE,cAAc,EAAC,uBAAuB;oBACtCgD,UAAU,EAAEnD,cAAe;oBAC3BmB,OAAO,EAAC,SAAS;oBACjBiC,eAAe,EAAE7F,cAAc,CAAC,kBAAkB;kBAAE;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlE,OAAA,CAACL,cAAc;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPlE,OAAA,CAACjC,IAAI;UAAC0E,IAAI;UAACoD,EAAE,EAAE,EAAG;UAAAtB,QAAA,eAChBvE,OAAA,CAAChC,IAAI;YAACsG,SAAS,EAAC,mBAAmB;YAACF,EAAE,EAAE;cAAEyD,SAAS,EAAE,OAAO;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAvD,QAAA,eACnFvE,OAAA,CAAC/B,WAAW;cAACmG,EAAE,EAAE;gBAAEyD,SAAS,EAAE;cAAQ,CAAE;cAAAtD,QAAA,gBACtCvE,OAAA,CAAC9B,GAAG;gBAAC4G,OAAO,EAAC,MAAM;gBAACyB,cAAc,EAAC,eAAe;gBAACtB,UAAU,EAAC,QAAQ;gBAACT,EAAE,EAAE,CAAE;gBAAAD,QAAA,gBAC3EvE,OAAA,CAAClC,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAACE,YAAY;kBAAAJ,QAAA,EAAC;gBAEtC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACblE,OAAA,CAAC9B,GAAG;kBAACkG,EAAE,EAAE;oBACPU,OAAO,EAAE,MAAM;oBACfG,UAAU,EAAE,QAAQ;oBACpBF,GAAG,EAAE,CAAC;oBACNmB,CAAC,EAAE,CAAC;oBACJC,eAAe,EAAE,kBAAkB;oBACnCyB,YAAY,EAAE,CAAC;oBACfxB,MAAM,EAAE,WAAW;oBACnBC,WAAW,EAAE;kBACf,CAAE;kBAAA9B,QAAA,gBACAvE,OAAA,CAAClC,UAAU;oBAAC2G,OAAO,EAAC,OAAO;oBAACI,KAAK,EAAC,gBAAgB;oBAACD,UAAU,EAAE,GAAI;oBAAAL,QAAA,EAAC;kBAEpE;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACblE,OAAA,CAACH,iBAAiB;oBAChB0D,YAAY,EAAC,mBAAmB;oBAChCE,cAAc,EAAC,mBAAmB;oBAClCgD,UAAU,EAAEnD,cAAe;oBAC3BmB,OAAO,EAAC,SAAS;oBACjBiC,eAAe,EAAE7F,cAAc,CAAC,mBAAmB;kBAAE;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlE,OAAA,CAACJ,gBAAgB;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNlE,OAAA,CAAC9B,GAAG;MAACsG,EAAE,EAAE,CAAE;MAAAD,QAAA,eACTvE,OAAA,CAACjC,IAAI;QAAC4H,SAAS;QAACC,OAAO,EAAE,CAAE;QAAArB,QAAA,eACzBvE,OAAA,CAACjC,IAAI;UAAC0E,IAAI;UAACoD,EAAE,EAAE,EAAG;UAAAtB,QAAA,eAClBvE,OAAA,CAAChC,IAAI;YACHoG,EAAE,EAAE;cACF8B,CAAC,EAAE,CAAC;cACJC,eAAe,EAAE,YAAY;cAC7BC,MAAM,EAAE,WAAW;cACnBC,WAAW,EAAE;YACf,CAAE;YAAA9B,QAAA,eAEFvE,OAAA,CAAC9B,GAAG;cAAC4G,OAAO,EAAC,MAAM;cAACG,UAAU,EAAC,QAAQ;cAACsB,cAAc,EAAC,QAAQ;cAACwB,aAAa,EAAC,QAAQ;cAAChD,GAAG,EAAE,CAAE;cAAAR,QAAA,gBAC5FvE,OAAA,CAAC9B,GAAG;gBAAC4G,OAAO,EAAC,MAAM;gBAACG,UAAU,EAAC,QAAQ;gBAACF,GAAG,EAAE,CAAE;gBAAAR,QAAA,gBAC7CvE,OAAA,CAAC9B,GAAG;kBACFkG,EAAE,EAAE;oBACFwC,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACVe,YAAY,EAAE,KAAK;oBACnBI,OAAO,EAAE,WAAW;oBACpBlD,OAAO,EAAE,MAAM;oBACfG,UAAU,EAAE,QAAQ;oBACpBsB,cAAc,EAAE,QAAQ;oBACxB1B,KAAK,EAAE,OAAO;oBACd2B,QAAQ,EAAE,MAAM;oBAChB5B,UAAU,EAAE;kBACd,CAAE;kBAAAL,QAAA,EACH;gBAED;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNlE,OAAA,CAAClC,UAAU;kBAAC2G,OAAO,EAAC,IAAI;kBAACG,UAAU,EAAC,MAAM;kBAAAL,QAAA,EAAC;gBAE3C;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAENlE,OAAA,CAAClC,UAAU;gBAAC2G,OAAO,EAAC,OAAO;gBAACI,KAAK,EAAC,gBAAgB;gBAACoD,SAAS,EAAC,QAAQ;gBAAC9D,QAAQ,EAAC,OAAO;gBAAAI,QAAA,EAAC;cAGvF;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEblE,OAAA,CAACH,iBAAiB;gBAChB0D,YAAY,EAAC,8BAA8B;gBAC3CE,cAAc,EAAC,sBAAsB;gBACrCgD,UAAU,EAAEnD,cAAe;gBAC3BmB,OAAO,EAAC,SAAS;gBACjBiC,eAAe,EAAE7F,cAAc,CAAC,8BAA8B;cAAE;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB;AAAChE,EAAA,CA3buBD,SAAS;EAAA,QAUYH,gBAAgB;AAAA;AAAAoI,EAAA,GAVrCjI,SAAS;AAAA,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}