import e from"@csstools/postcss-progressive-custom-properties";import s from"postcss-value-parser";const t=e=>({postcssPlugin:"postcss-ic-unit",Declaration(t){if(!t.value.toLowerCase().includes("ic"))return;if(function(e){let s=e.parent;for(;s;)if("atrule"===s.type){if("supports"===s.name.toLowerCase()&&/\(font-size: \d+ic\)/.test(s.params.toLowerCase()))return!0;s=s.parent}else s=s.parent;return!1}(t))return;const r=s(t.value);r.walk((e=>{if(!e.type||"word"!==e.type)return;const t=s.unit(e.value);t&&"ic"===t.unit.toLowerCase()&&(e.value=`${t.number}em`)}));const o=String(r);o!==t.value&&(t.cloneBefore({value:o}),e.preserve||t.remove())}});t.postcss=!0;const r=s=>{const r=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},s);return r.enableProgressiveCustomProperties&&r.preserve?{postcssPlugin:"postcss-ic-unit",plugins:[e(),t(r)]}:t(r)};r.postcss=!0;export{r as default};
