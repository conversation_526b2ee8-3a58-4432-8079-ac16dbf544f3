{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\capstone final project\\\\SafeRoomAI-Athira\\\\frontend\\\\src\\\\screens\\\\ActivityFeed.jsx\",\n  _s = $RefreshSig$();\n// src/screens/ActivityFeed.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Container, Typography, Grid, Card, CardMedia, CardContent, Box, Chip, TextField, InputAdornment, IconButton, Dialog, DialogContent, DialogTitle, Pagination, Alert } from '@mui/material';\nimport { Search, Refresh, Warning, Close, AccessTime } from '@mui/icons-material';\nimport { format } from 'date-fns';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorAlert from '../components/ErrorAlert';\nimport AIFeedbackButtons from '../components/AIFeedbackButtons';\nimport feedbackService from '../services/feedbackService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function ActivityFeed() {\n  _s();\n  const [files, setFiles] = useState([]);\n  const [filteredFiles, setFilteredFiles] = useState([]);\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedImage, setSelectedImage] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [feedbackStates, setFeedbackStates] = useState({});\n  const itemsPerPage = 12;\n  useEffect(() => {\n    async function fetchList() {\n      try {\n        setLoading(true);\n        const res = await fetch('/predict/activity/list');\n        if (!res.ok) throw new Error(`HTTP ${res.status}`);\n        const data = await res.json();\n        if (Array.isArray(data)) {\n          setFiles(data);\n          setError(null);\n        } else {\n          console.warn('Expected an array but got:', data);\n          setFiles([]);\n          setError('No activity found.');\n        }\n      } catch (e) {\n        console.error('Error fetching activity list:', e);\n        setFiles([]);\n        setError('Failed to load activity.');\n      } finally {\n        setLoading(false);\n      }\n    }\n\n    // Initial fetch + auto-refresh every 10s\n    fetchList();\n    const interval = setInterval(fetchList, 10000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // Filter files based on search term\n  useEffect(() => {\n    if (!searchTerm) {\n      setFilteredFiles(files);\n    } else {\n      const filtered = files.filter(fname => {\n        const prefix = fname.slice(0, 15);\n        const iso = `${prefix.slice(0, 4)}-${prefix.slice(4, 6)}-${prefix.slice(6, 8)}T${prefix.slice(9, 11)}:${prefix.slice(11, 13)}:${prefix.slice(13, 15)}`;\n        const displayTime = new Date(iso).toLocaleString();\n        return displayTime.toLowerCase().includes(searchTerm.toLowerCase());\n      });\n      setFilteredFiles(filtered);\n    }\n    setCurrentPage(1); // Reset to first page when filtering\n  }, [files, searchTerm]);\n  const handleRefresh = async () => {\n    setLoading(true);\n    try {\n      const res = await fetch('/predict/activity/list');\n      if (!res.ok) throw new Error(`HTTP ${res.status}`);\n      const data = await res.json();\n      if (Array.isArray(data)) {\n        setFiles(data);\n        setError(null);\n      }\n    } catch (e) {\n      setError('Failed to refresh activity.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatTimestamp = fname => {\n    const prefix = fname.slice(0, 15);\n    const iso = `${prefix.slice(0, 4)}-${prefix.slice(4, 6)}-${prefix.slice(6, 8)}T${prefix.slice(9, 11)}:${prefix.slice(11, 13)}:${prefix.slice(13, 15)}`;\n    return new Date(iso);\n  };\n  const handleFeedback = async (suggestionId, feedbackType, suggestionType) => {\n    try {\n      await feedbackService.submitFeedback(suggestionId, feedbackType, suggestionType, {\n        filename: suggestionId,\n        timestamp: formatTimestamp(suggestionId).toISOString()\n      });\n      setFeedbackStates(prev => ({\n        ...prev,\n        [suggestionId]: feedbackType\n      }));\n    } catch (error) {\n      console.error('Failed to submit feedback:', error);\n    }\n  };\n\n  // Pagination\n  const totalPages = Math.ceil(filteredFiles.length / itemsPerPage);\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const paginatedFiles = filteredFiles.slice(startIndex, startIndex + itemsPerPage);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      message: \"Loading activity feed...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        fontWeight: \"bold\",\n        children: \"Activity Feed\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"Recent anomaly detections and security events\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      gap: 2,\n      mb: 3,\n      flexWrap: \"wrap\",\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        placeholder: \"Search by date/time...\",\n        value: searchTerm,\n        onChange: e => setSearchTerm(e.target.value),\n        size: \"small\",\n        sx: {\n          minWidth: 250\n        },\n        InputProps: {\n          startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"start\",\n            children: /*#__PURE__*/_jsxDEV(Search, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 15\n          }, this)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: handleRefresh,\n        disabled: loading,\n        children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        icon: /*#__PURE__*/_jsxDEV(AccessTime, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 17\n        }, this),\n        label: `${filteredFiles.length} events`,\n        color: \"primary\",\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(ErrorAlert, {\n      message: error,\n      onRetry: handleRefresh,\n      sx: {\n        mb: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 9\n    }, this), filteredFiles.length === 0 && !error ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 3\n      },\n      children: [\"No activity snapshots found. \", searchTerm && 'Try adjusting your search terms.']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: paginatedFiles.map(fname => {\n          const timestamp = formatTimestamp(fname);\n          const displayTime = format(timestamp, 'MMM dd, yyyy HH:mm:ss');\n          return /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            lg: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                border: feedbackStates[fname] === 'accept' ? '2px solid #10b981' : feedbackStates[fname] === 'reject' ? '2px solid #ef4444' : '1px solid',\n                borderColor: feedbackStates[fname] ? 'transparent' : 'divider',\n                boxShadow: feedbackStates[fname] === 'accept' ? '0 4px 12px rgba(16, 185, 129, 0.2)' : feedbackStates[fname] === 'reject' ? '0 4px 12px rgba(239, 68, 68, 0.2)' : undefined,\n                '&:hover': {\n                  transform: 'scale(1.02)',\n                  boxShadow: feedbackStates[fname] === 'accept' ? '0 6px 16px rgba(16, 185, 129, 0.3)' : feedbackStates[fname] === 'reject' ? '0 6px 16px rgba(239, 68, 68, 0.3)' : '0 4px 12px rgba(0,0,0,0.1)'\n                }\n              },\n              onClick: () => setSelectedImage({\n                fname,\n                timestamp\n              }),\n              children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n                component: \"img\",\n                height: \"200\",\n                image: `/predict/activity/${fname}`,\n                alt: `Anomaly at ${displayTime}`,\n                sx: {\n                  objectFit: 'cover'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  mb: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Warning, {\n                    color: \"warning\",\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"warning.main\",\n                    fontWeight: \"bold\",\n                    children: \"ANOMALY DETECTED\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 25\n                  }, this), feedbackStates[fname] && /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: feedbackStates[fname] === 'accept' ? 'Helpful' : 'Not helpful',\n                    color: feedbackStates[fname] === 'accept' ? 'success' : 'error',\n                    sx: {\n                      fontSize: '0.6rem',\n                      height: '16px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  mb: 1,\n                  children: displayTime\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  onClick: e => e.stopPropagation() // Prevent card click when interacting with feedback\n                  ,\n                  sx: {\n                    mt: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(AIFeedbackButtons, {\n                    suggestionId: fname,\n                    suggestionType: \"anomaly_detection\",\n                    onFeedback: handleFeedback,\n                    variant: \"compact\",\n                    initialFeedback: feedbackStates[fname]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this)\n          }, fname, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 11\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        mt: 4,\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: totalPages,\n          page: currentPage,\n          onChange: (e, page) => setCurrentPage(page),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: !!selectedImage,\n      onClose: () => setSelectedImage(null),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: selectedImage && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: [\"Anomaly Detection - \", format(selectedImage.timestamp, 'MMM dd, yyyy HH:mm:ss')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => setSelectedImage(null),\n              children: /*#__PURE__*/_jsxDEV(Close, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `/predict/activity/${selectedImage.fname}`,\n            alt: \"Anomaly detail\",\n            style: {\n              width: '100%',\n              height: 'auto',\n              borderRadius: '8px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n}\n_s(ActivityFeed, \"vFrBhwc3NvhQzSZEp3ihyFDkSt0=\");\n_c = ActivityFeed;\nvar _c;\n$RefreshReg$(_c, \"ActivityFeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Typography", "Grid", "Card", "CardMedia", "<PERSON><PERSON><PERSON><PERSON>", "Box", "Chip", "TextField", "InputAdornment", "IconButton", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "Pagination", "<PERSON><PERSON>", "Search", "Refresh", "Warning", "Close", "AccessTime", "format", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AIFeedbackButtons", "feedbackService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ActivityFeed", "_s", "files", "setFiles", "filteredFiles", "setFilteredFiles", "error", "setError", "loading", "setLoading", "searchTerm", "setSearchTerm", "selectedImage", "setSelectedImage", "currentPage", "setCurrentPage", "feedbackStates", "setFeedbackStates", "itemsPerPage", "fetchList", "res", "fetch", "ok", "Error", "status", "data", "json", "Array", "isArray", "console", "warn", "e", "interval", "setInterval", "clearInterval", "filtered", "filter", "fname", "prefix", "slice", "iso", "displayTime", "Date", "toLocaleString", "toLowerCase", "includes", "handleRefresh", "formatTimestamp", "handleFeedback", "suggestionId", "feedbackType", "suggestionType", "submitFeedback", "filename", "timestamp", "toISOString", "prev", "totalPages", "Math", "ceil", "length", "startIndex", "paginatedFiles", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "children", "mb", "variant", "component", "gutterBottom", "fontWeight", "color", "display", "gap", "flexWrap", "placeholder", "value", "onChange", "target", "size", "sx", "min<PERSON><PERSON><PERSON>", "InputProps", "startAdornment", "position", "onClick", "disabled", "icon", "label", "onRetry", "severity", "container", "spacing", "map", "item", "xs", "sm", "md", "lg", "cursor", "transition", "border", "borderColor", "boxShadow", "undefined", "transform", "height", "image", "alt", "objectFit", "p", "alignItems", "fontSize", "stopPropagation", "mt", "onFeedback", "initialFeedback", "justifyContent", "count", "page", "open", "onClose", "fullWidth", "src", "style", "width", "borderRadius", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/capstone final project/SafeRoomAI-Athira/frontend/src/screens/ActivityFeed.jsx"], "sourcesContent": ["// src/screens/ActivityFeed.jsx\r\nimport React, { useState, useEffect } from 'react';\r\nimport {\r\n  Container,\r\n  Typography,\r\n  Grid,\r\n  Card,\r\n  CardMedia,\r\n  CardContent,\r\n  Box,\r\n  Chip,\r\n  TextField,\r\n  InputAdornment,\r\n  IconButton,\r\n  Dialog,\r\n  DialogContent,\r\n  DialogTitle,\r\n  Pagination,\r\n  Alert\r\n} from '@mui/material';\r\nimport {\r\n  Search,\r\n  Refresh,\r\n  Warning,\r\n  Close,\r\n  AccessTime\r\n} from '@mui/icons-material';\r\nimport { format } from 'date-fns';\r\nimport LoadingSpinner from '../components/LoadingSpinner';\r\nimport ErrorAlert from '../components/ErrorAlert';\r\nimport AIFeedbackButtons from '../components/AIFeedbackButtons';\r\nimport feedbackService from '../services/feedbackService';\r\n\r\nexport default function ActivityFeed() {\r\n  const [files, setFiles] = useState([]);\r\n  const [filteredFiles, setFilteredFiles] = useState([]);\r\n  const [error, setError] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedImage, setSelectedImage] = useState(null);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [feedbackStates, setFeedbackStates] = useState({});\r\n  const itemsPerPage = 12;\r\n\r\n  useEffect(() => {\r\n    async function fetchList() {\r\n      try {\r\n        setLoading(true);\r\n        const res = await fetch('/predict/activity/list');\r\n        if (!res.ok) throw new Error(`HTTP ${res.status}`);\r\n        const data = await res.json();\r\n\r\n        if (Array.isArray(data)) {\r\n          setFiles(data);\r\n          setError(null);\r\n        } else {\r\n          console.warn('Expected an array but got:', data);\r\n          setFiles([]);\r\n          setError('No activity found.');\r\n        }\r\n      } catch (e) {\r\n        console.error('Error fetching activity list:', e);\r\n        setFiles([]);\r\n        setError('Failed to load activity.');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }\r\n\r\n    // Initial fetch + auto-refresh every 10s\r\n    fetchList();\r\n    const interval = setInterval(fetchList, 10000);\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  // Filter files based on search term\r\n  useEffect(() => {\r\n    if (!searchTerm) {\r\n      setFilteredFiles(files);\r\n    } else {\r\n      const filtered = files.filter(fname => {\r\n        const prefix = fname.slice(0, 15);\r\n        const iso = `${prefix.slice(0, 4)}-${prefix.slice(4, 6)}-${prefix.slice(6, 8)}T${prefix.slice(9, 11)}:${prefix.slice(11, 13)}:${prefix.slice(13, 15)}`;\r\n        const displayTime = new Date(iso).toLocaleString();\r\n        return displayTime.toLowerCase().includes(searchTerm.toLowerCase());\r\n      });\r\n      setFilteredFiles(filtered);\r\n    }\r\n    setCurrentPage(1); // Reset to first page when filtering\r\n  }, [files, searchTerm]);\r\n\r\n  const handleRefresh = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const res = await fetch('/predict/activity/list');\r\n      if (!res.ok) throw new Error(`HTTP ${res.status}`);\r\n      const data = await res.json();\r\n      if (Array.isArray(data)) {\r\n        setFiles(data);\r\n        setError(null);\r\n      }\r\n    } catch (e) {\r\n      setError('Failed to refresh activity.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const formatTimestamp = (fname) => {\r\n    const prefix = fname.slice(0, 15);\r\n    const iso = `${prefix.slice(0, 4)}-${prefix.slice(4, 6)}-${prefix.slice(6, 8)}T${prefix.slice(9, 11)}:${prefix.slice(11, 13)}:${prefix.slice(13, 15)}`;\r\n    return new Date(iso);\r\n  };\r\n\r\n  const handleFeedback = async (suggestionId, feedbackType, suggestionType) => {\r\n    try {\r\n      await feedbackService.submitFeedback(suggestionId, feedbackType, suggestionType, {\r\n        filename: suggestionId,\r\n        timestamp: formatTimestamp(suggestionId).toISOString()\r\n      });\r\n\r\n      setFeedbackStates(prev => ({\r\n        ...prev,\r\n        [suggestionId]: feedbackType\r\n      }));\r\n    } catch (error) {\r\n      console.error('Failed to submit feedback:', error);\r\n    }\r\n  };\r\n\r\n  // Pagination\r\n  const totalPages = Math.ceil(filteredFiles.length / itemsPerPage);\r\n  const startIndex = (currentPage - 1) * itemsPerPage;\r\n  const paginatedFiles = filteredFiles.slice(startIndex, startIndex + itemsPerPage);\r\n\r\n  if (loading) {\r\n    return <LoadingSpinner message=\"Loading activity feed...\" />;\r\n  }\r\n\r\n  return (\r\n    <Container maxWidth=\"xl\">\r\n      <Box mb={3}>\r\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom fontWeight=\"bold\">\r\n          Activity Feed\r\n        </Typography>\r\n        <Typography variant=\"body1\" color=\"text.secondary\">\r\n          Recent anomaly detections and security events\r\n        </Typography>\r\n      </Box>\r\n\r\n      {/* Controls */}\r\n      <Box display=\"flex\" gap={2} mb={3} flexWrap=\"wrap\">\r\n        <TextField\r\n          placeholder=\"Search by date/time...\"\r\n          value={searchTerm}\r\n          onChange={(e) => setSearchTerm(e.target.value)}\r\n          size=\"small\"\r\n          sx={{ minWidth: 250 }}\r\n          InputProps={{\r\n            startAdornment: (\r\n              <InputAdornment position=\"start\">\r\n                <Search />\r\n              </InputAdornment>\r\n            ),\r\n          }}\r\n        />\r\n        <IconButton onClick={handleRefresh} disabled={loading}>\r\n          <Refresh />\r\n        </IconButton>\r\n        <Chip\r\n          icon={<AccessTime />}\r\n          label={`${filteredFiles.length} events`}\r\n          color=\"primary\"\r\n          variant=\"outlined\"\r\n        />\r\n      </Box>\r\n\r\n      {error && (\r\n        <ErrorAlert\r\n          message={error}\r\n          onRetry={handleRefresh}\r\n          sx={{ mb: 3 }}\r\n        />\r\n      )}\r\n\r\n      {filteredFiles.length === 0 && !error ? (\r\n        <Alert severity=\"info\" sx={{ mb: 3 }}>\r\n          No activity snapshots found. {searchTerm && 'Try adjusting your search terms.'}\r\n        </Alert>\r\n      ) : (\r\n        <>\r\n          <Grid container spacing={2}>\r\n            {paginatedFiles.map((fname) => {\r\n              const timestamp = formatTimestamp(fname);\r\n              const displayTime = format(timestamp, 'MMM dd, yyyy HH:mm:ss');\r\n\r\n              return (\r\n                <Grid item xs={12} sm={6} md={4} lg={3} key={fname}>\r\n                  <Card\r\n                    sx={{\r\n                      cursor: 'pointer',\r\n                      transition: 'all 0.3s ease',\r\n                      border: feedbackStates[fname] === 'accept'\r\n                        ? '2px solid #10b981'\r\n                        : feedbackStates[fname] === 'reject'\r\n                        ? '2px solid #ef4444'\r\n                        : '1px solid',\r\n                      borderColor: feedbackStates[fname] ? 'transparent' : 'divider',\r\n                      boxShadow: feedbackStates[fname] === 'accept'\r\n                        ? '0 4px 12px rgba(16, 185, 129, 0.2)'\r\n                        : feedbackStates[fname] === 'reject'\r\n                        ? '0 4px 12px rgba(239, 68, 68, 0.2)'\r\n                        : undefined,\r\n                      '&:hover': {\r\n                        transform: 'scale(1.02)',\r\n                        boxShadow: feedbackStates[fname] === 'accept'\r\n                          ? '0 6px 16px rgba(16, 185, 129, 0.3)'\r\n                          : feedbackStates[fname] === 'reject'\r\n                          ? '0 6px 16px rgba(239, 68, 68, 0.3)'\r\n                          : '0 4px 12px rgba(0,0,0,0.1)'\r\n                      }\r\n                    }}\r\n                    onClick={() => setSelectedImage({ fname, timestamp })}\r\n                  >\r\n                    <CardMedia\r\n                      component=\"img\"\r\n                      height=\"200\"\r\n                      image={`/predict/activity/${fname}`}\r\n                      alt={`Anomaly at ${displayTime}`}\r\n                      sx={{ objectFit: 'cover' }}\r\n                    />\r\n                    <CardContent sx={{ p: 2 }}>\r\n                      <Box display=\"flex\" alignItems=\"center\" gap={1} mb={1}>\r\n                        <Warning color=\"warning\" fontSize=\"small\" />\r\n                        <Typography variant=\"caption\" color=\"warning.main\" fontWeight=\"bold\">\r\n                          ANOMALY DETECTED\r\n                        </Typography>\r\n                        {feedbackStates[fname] && (\r\n                          <Chip\r\n                            size=\"small\"\r\n                            label={feedbackStates[fname] === 'accept' ? 'Helpful' : 'Not helpful'}\r\n                            color={feedbackStates[fname] === 'accept' ? 'success' : 'error'}\r\n                            sx={{ fontSize: '0.6rem', height: '16px' }}\r\n                          />\r\n                        )}\r\n                      </Box>\r\n                      <Typography variant=\"body2\" color=\"text.secondary\" mb={1}>\r\n                        {displayTime}\r\n                      </Typography>\r\n\r\n                      {/* AI Feedback Buttons */}\r\n                      <Box\r\n                        onClick={(e) => e.stopPropagation()} // Prevent card click when interacting with feedback\r\n                        sx={{ mt: 1 }}\r\n                      >\r\n                        <AIFeedbackButtons\r\n                          suggestionId={fname}\r\n                          suggestionType=\"anomaly_detection\"\r\n                          onFeedback={handleFeedback}\r\n                          variant=\"compact\"\r\n                          initialFeedback={feedbackStates[fname]}\r\n                        />\r\n                      </Box>\r\n                    </CardContent>\r\n                  </Card>\r\n                </Grid>\r\n              );\r\n            })}\r\n          </Grid>\r\n\r\n          {/* Pagination */}\r\n          {totalPages > 1 && (\r\n            <Box display=\"flex\" justifyContent=\"center\" mt={4}>\r\n              <Pagination\r\n                count={totalPages}\r\n                page={currentPage}\r\n                onChange={(e, page) => setCurrentPage(page)}\r\n                color=\"primary\"\r\n              />\r\n            </Box>\r\n          )}\r\n        </>\r\n      )}\r\n\r\n      {/* Image Modal */}\r\n      <Dialog\r\n        open={!!selectedImage}\r\n        onClose={() => setSelectedImage(null)}\r\n        maxWidth=\"md\"\r\n        fullWidth\r\n      >\r\n        {selectedImage && (\r\n          <>\r\n            <DialogTitle>\r\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\r\n                <Typography variant=\"h6\">\r\n                  Anomaly Detection - {format(selectedImage.timestamp, 'MMM dd, yyyy HH:mm:ss')}\r\n                </Typography>\r\n                <IconButton onClick={() => setSelectedImage(null)}>\r\n                  <Close />\r\n                </IconButton>\r\n              </Box>\r\n            </DialogTitle>\r\n            <DialogContent>\r\n              <img\r\n                src={`/predict/activity/${selectedImage.fname}`}\r\n                alt=\"Anomaly detail\"\r\n                style={{ width: '100%', height: 'auto', borderRadius: '8px' }}\r\n              />\r\n            </DialogContent>\r\n          </>\r\n        )}\r\n      </Dialog>\r\n    </Container>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,GAAG,EACHC,IAAI,EACJC,SAAS,EACTC,cAAc,EACdC,UAAU,EACVC,MAAM,EACNC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SACEC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,KAAK,EACLC,UAAU,QACL,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,UAAU;AACjC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,eAAe,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,eAAe,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACrC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAMkD,YAAY,GAAG,EAAE;EAEvBjD,SAAS,CAAC,MAAM;IACd,eAAekD,SAASA,CAAA,EAAG;MACzB,IAAI;QACFV,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMW,GAAG,GAAG,MAAMC,KAAK,CAAC,wBAAwB,CAAC;QACjD,IAAI,CAACD,GAAG,CAACE,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,QAAQH,GAAG,CAACI,MAAM,EAAE,CAAC;QAClD,MAAMC,IAAI,GAAG,MAAML,GAAG,CAACM,IAAI,CAAC,CAAC;QAE7B,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;UACvBtB,QAAQ,CAACsB,IAAI,CAAC;UACdlB,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACLsB,OAAO,CAACC,IAAI,CAAC,4BAA4B,EAAEL,IAAI,CAAC;UAChDtB,QAAQ,CAAC,EAAE,CAAC;UACZI,QAAQ,CAAC,oBAAoB,CAAC;QAChC;MACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;QACVF,OAAO,CAACvB,KAAK,CAAC,+BAA+B,EAAEyB,CAAC,CAAC;QACjD5B,QAAQ,CAAC,EAAE,CAAC;QACZI,QAAQ,CAAC,0BAA0B,CAAC;MACtC,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;;IAEA;IACAU,SAAS,CAAC,CAAC;IACX,MAAMa,QAAQ,GAAGC,WAAW,CAACd,SAAS,EAAE,KAAK,CAAC;IAC9C,OAAO,MAAMe,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA/D,SAAS,CAAC,MAAM;IACd,IAAI,CAACyC,UAAU,EAAE;MACfL,gBAAgB,CAACH,KAAK,CAAC;IACzB,CAAC,MAAM;MACL,MAAMiC,QAAQ,GAAGjC,KAAK,CAACkC,MAAM,CAACC,KAAK,IAAI;QACrC,MAAMC,MAAM,GAAGD,KAAK,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;QACjC,MAAMC,GAAG,GAAG,GAAGF,MAAM,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAID,MAAM,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAID,MAAM,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAID,MAAM,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,IAAID,MAAM,CAACC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,IAAID,MAAM,CAACC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;QACtJ,MAAME,WAAW,GAAG,IAAIC,IAAI,CAACF,GAAG,CAAC,CAACG,cAAc,CAAC,CAAC;QAClD,OAAOF,WAAW,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnC,UAAU,CAACkC,WAAW,CAAC,CAAC,CAAC;MACrE,CAAC,CAAC;MACFvC,gBAAgB,CAAC8B,QAAQ,CAAC;IAC5B;IACApB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,EAAE,CAACb,KAAK,EAAEQ,UAAU,CAAC,CAAC;EAEvB,MAAMoC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCrC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMW,GAAG,GAAG,MAAMC,KAAK,CAAC,wBAAwB,CAAC;MACjD,IAAI,CAACD,GAAG,CAACE,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,QAAQH,GAAG,CAACI,MAAM,EAAE,CAAC;MAClD,MAAMC,IAAI,GAAG,MAAML,GAAG,CAACM,IAAI,CAAC,CAAC;MAC7B,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;QACvBtB,QAAQ,CAACsB,IAAI,CAAC;QACdlB,QAAQ,CAAC,IAAI,CAAC;MAChB;IACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;MACVxB,QAAQ,CAAC,6BAA6B,CAAC;IACzC,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,eAAe,GAAIV,KAAK,IAAK;IACjC,MAAMC,MAAM,GAAGD,KAAK,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IACjC,MAAMC,GAAG,GAAG,GAAGF,MAAM,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAID,MAAM,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAID,MAAM,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAID,MAAM,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,IAAID,MAAM,CAACC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,IAAID,MAAM,CAACC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;IACtJ,OAAO,IAAIG,IAAI,CAACF,GAAG,CAAC;EACtB,CAAC;EAED,MAAMQ,cAAc,GAAG,MAAAA,CAAOC,YAAY,EAAEC,YAAY,EAAEC,cAAc,KAAK;IAC3E,IAAI;MACF,MAAMxD,eAAe,CAACyD,cAAc,CAACH,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAE;QAC/EE,QAAQ,EAAEJ,YAAY;QACtBK,SAAS,EAAEP,eAAe,CAACE,YAAY,CAAC,CAACM,WAAW,CAAC;MACvD,CAAC,CAAC;MAEFtC,iBAAiB,CAACuC,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP,CAACP,YAAY,GAAGC;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACduB,OAAO,CAACvB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMmD,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACvD,aAAa,CAACwD,MAAM,GAAG1C,YAAY,CAAC;EACjE,MAAM2C,UAAU,GAAG,CAAC/C,WAAW,GAAG,CAAC,IAAII,YAAY;EACnD,MAAM4C,cAAc,GAAG1D,aAAa,CAACmC,KAAK,CAACsB,UAAU,EAAEA,UAAU,GAAG3C,YAAY,CAAC;EAEjF,IAAIV,OAAO,EAAE;IACX,oBAAOX,OAAA,CAACL,cAAc;MAACuE,OAAO,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC9D;EAEA,oBACEtE,OAAA,CAAC3B,SAAS;IAACkG,QAAQ,EAAC,IAAI;IAAAC,QAAA,gBACtBxE,OAAA,CAACrB,GAAG;MAAC8F,EAAE,EAAE,CAAE;MAAAD,QAAA,gBACTxE,OAAA,CAAC1B,UAAU;QAACoG,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAACC,UAAU,EAAC,MAAM;QAAAL,QAAA,EAAC;MAEvE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbtE,OAAA,CAAC1B,UAAU;QAACoG,OAAO,EAAC,OAAO;QAACI,KAAK,EAAC,gBAAgB;QAAAN,QAAA,EAAC;MAEnD;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNtE,OAAA,CAACrB,GAAG;MAACoG,OAAO,EAAC,MAAM;MAACC,GAAG,EAAE,CAAE;MAACP,EAAE,EAAE,CAAE;MAACQ,QAAQ,EAAC,MAAM;MAAAT,QAAA,gBAChDxE,OAAA,CAACnB,SAAS;QACRqG,WAAW,EAAC,wBAAwB;QACpCC,KAAK,EAAEtE,UAAW;QAClBuE,QAAQ,EAAGlD,CAAC,IAAKpB,aAAa,CAACoB,CAAC,CAACmD,MAAM,CAACF,KAAK,CAAE;QAC/CG,IAAI,EAAC,OAAO;QACZC,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QACtBC,UAAU,EAAE;UACVC,cAAc,eACZ1F,OAAA,CAAClB,cAAc;YAAC6G,QAAQ,EAAC,OAAO;YAAAnB,QAAA,eAC9BxE,OAAA,CAACX,MAAM;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAEpB;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFtE,OAAA,CAACjB,UAAU;QAAC6G,OAAO,EAAE3C,aAAc;QAAC4C,QAAQ,EAAElF,OAAQ;QAAA6D,QAAA,eACpDxE,OAAA,CAACV,OAAO;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACbtE,OAAA,CAACpB,IAAI;QACHkH,IAAI,eAAE9F,OAAA,CAACP,UAAU;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACrByB,KAAK,EAAE,GAAGxF,aAAa,CAACwD,MAAM,SAAU;QACxCe,KAAK,EAAC,SAAS;QACfJ,OAAO,EAAC;MAAU;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEL7D,KAAK,iBACJT,OAAA,CAACJ,UAAU;MACTsE,OAAO,EAAEzD,KAAM;MACfuF,OAAO,EAAE/C,aAAc;MACvBsC,EAAE,EAAE;QAAEd,EAAE,EAAE;MAAE;IAAE;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CACF,EAEA/D,aAAa,CAACwD,MAAM,KAAK,CAAC,IAAI,CAACtD,KAAK,gBACnCT,OAAA,CAACZ,KAAK;MAAC6G,QAAQ,EAAC,MAAM;MAACV,EAAE,EAAE;QAAEd,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,+BACP,EAAC3D,UAAU,IAAI,kCAAkC;IAAA;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzE,CAAC,gBAERtE,OAAA,CAAAE,SAAA;MAAAsE,QAAA,gBACExE,OAAA,CAACzB,IAAI;QAAC2H,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA3B,QAAA,EACxBP,cAAc,CAACmC,GAAG,CAAE5D,KAAK,IAAK;UAC7B,MAAMiB,SAAS,GAAGP,eAAe,CAACV,KAAK,CAAC;UACxC,MAAMI,WAAW,GAAGlD,MAAM,CAAC+D,SAAS,EAAE,uBAAuB,CAAC;UAE9D,oBACEzD,OAAA,CAACzB,IAAI;YAAC8H,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjC,QAAA,eACrCxE,OAAA,CAACxB,IAAI;cACH+G,EAAE,EAAE;gBACFmB,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,eAAe;gBAC3BC,MAAM,EAAEzF,cAAc,CAACqB,KAAK,CAAC,KAAK,QAAQ,GACtC,mBAAmB,GACnBrB,cAAc,CAACqB,KAAK,CAAC,KAAK,QAAQ,GAClC,mBAAmB,GACnB,WAAW;gBACfqE,WAAW,EAAE1F,cAAc,CAACqB,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;gBAC9DsE,SAAS,EAAE3F,cAAc,CAACqB,KAAK,CAAC,KAAK,QAAQ,GACzC,oCAAoC,GACpCrB,cAAc,CAACqB,KAAK,CAAC,KAAK,QAAQ,GAClC,mCAAmC,GACnCuE,SAAS;gBACb,SAAS,EAAE;kBACTC,SAAS,EAAE,aAAa;kBACxBF,SAAS,EAAE3F,cAAc,CAACqB,KAAK,CAAC,KAAK,QAAQ,GACzC,oCAAoC,GACpCrB,cAAc,CAACqB,KAAK,CAAC,KAAK,QAAQ,GAClC,mCAAmC,GACnC;gBACN;cACF,CAAE;cACFoD,OAAO,EAAEA,CAAA,KAAM5E,gBAAgB,CAAC;gBAAEwB,KAAK;gBAAEiB;cAAU,CAAC,CAAE;cAAAe,QAAA,gBAEtDxE,OAAA,CAACvB,SAAS;gBACRkG,SAAS,EAAC,KAAK;gBACfsC,MAAM,EAAC,KAAK;gBACZC,KAAK,EAAE,qBAAqB1E,KAAK,EAAG;gBACpC2E,GAAG,EAAE,cAAcvE,WAAW,EAAG;gBACjC2C,EAAE,EAAE;kBAAE6B,SAAS,EAAE;gBAAQ;cAAE;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFtE,OAAA,CAACtB,WAAW;gBAAC6G,EAAE,EAAE;kBAAE8B,CAAC,EAAE;gBAAE,CAAE;gBAAA7C,QAAA,gBACxBxE,OAAA,CAACrB,GAAG;kBAACoG,OAAO,EAAC,MAAM;kBAACuC,UAAU,EAAC,QAAQ;kBAACtC,GAAG,EAAE,CAAE;kBAACP,EAAE,EAAE,CAAE;kBAAAD,QAAA,gBACpDxE,OAAA,CAACT,OAAO;oBAACuF,KAAK,EAAC,SAAS;oBAACyC,QAAQ,EAAC;kBAAO;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CtE,OAAA,CAAC1B,UAAU;oBAACoG,OAAO,EAAC,SAAS;oBAACI,KAAK,EAAC,cAAc;oBAACD,UAAU,EAAC,MAAM;oBAAAL,QAAA,EAAC;kBAErE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EACZnD,cAAc,CAACqB,KAAK,CAAC,iBACpBxC,OAAA,CAACpB,IAAI;oBACH0G,IAAI,EAAC,OAAO;oBACZS,KAAK,EAAE5E,cAAc,CAACqB,KAAK,CAAC,KAAK,QAAQ,GAAG,SAAS,GAAG,aAAc;oBACtEsC,KAAK,EAAE3D,cAAc,CAACqB,KAAK,CAAC,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;oBAChE+C,EAAE,EAAE;sBAAEgC,QAAQ,EAAE,QAAQ;sBAAEN,MAAM,EAAE;oBAAO;kBAAE;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNtE,OAAA,CAAC1B,UAAU;kBAACoG,OAAO,EAAC,OAAO;kBAACI,KAAK,EAAC,gBAAgB;kBAACL,EAAE,EAAE,CAAE;kBAAAD,QAAA,EACtD5B;gBAAW;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAGbtE,OAAA,CAACrB,GAAG;kBACFiH,OAAO,EAAG1D,CAAC,IAAKA,CAAC,CAACsF,eAAe,CAAC,CAAE,CAAC;kBAAA;kBACrCjC,EAAE,EAAE;oBAAEkC,EAAE,EAAE;kBAAE,CAAE;kBAAAjD,QAAA,eAEdxE,OAAA,CAACH,iBAAiB;oBAChBuD,YAAY,EAAEZ,KAAM;oBACpBc,cAAc,EAAC,mBAAmB;oBAClCoE,UAAU,EAAEvE,cAAe;oBAC3BuB,OAAO,EAAC,SAAS;oBACjBiD,eAAe,EAAExG,cAAc,CAACqB,KAAK;kBAAE;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAnEoC9B,KAAK;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoE5C,CAAC;QAEX,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGNV,UAAU,GAAG,CAAC,iBACb5D,OAAA,CAACrB,GAAG;QAACoG,OAAO,EAAC,MAAM;QAAC6C,cAAc,EAAC,QAAQ;QAACH,EAAE,EAAE,CAAE;QAAAjD,QAAA,eAChDxE,OAAA,CAACb,UAAU;UACT0I,KAAK,EAAEjE,UAAW;UAClBkE,IAAI,EAAE7G,WAAY;UAClBmE,QAAQ,EAAEA,CAAClD,CAAC,EAAE4F,IAAI,KAAK5G,cAAc,CAAC4G,IAAI,CAAE;UAC5ChD,KAAK,EAAC;QAAS;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CACH,eAGDtE,OAAA,CAAChB,MAAM;MACL+I,IAAI,EAAE,CAAC,CAAChH,aAAc;MACtBiH,OAAO,EAAEA,CAAA,KAAMhH,gBAAgB,CAAC,IAAI,CAAE;MACtCuD,QAAQ,EAAC,IAAI;MACb0D,SAAS;MAAAzD,QAAA,EAERzD,aAAa,iBACZf,OAAA,CAAAE,SAAA;QAAAsE,QAAA,gBACExE,OAAA,CAACd,WAAW;UAAAsF,QAAA,eACVxE,OAAA,CAACrB,GAAG;YAACoG,OAAO,EAAC,MAAM;YAAC6C,cAAc,EAAC,eAAe;YAACN,UAAU,EAAC,QAAQ;YAAA9C,QAAA,gBACpExE,OAAA,CAAC1B,UAAU;cAACoG,OAAO,EAAC,IAAI;cAAAF,QAAA,GAAC,sBACH,EAAC9E,MAAM,CAACqB,aAAa,CAAC0C,SAAS,EAAE,uBAAuB,CAAC;YAAA;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACbtE,OAAA,CAACjB,UAAU;cAAC6G,OAAO,EAAEA,CAAA,KAAM5E,gBAAgB,CAAC,IAAI,CAAE;cAAAwD,QAAA,eAChDxE,OAAA,CAACR,KAAK;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACdtE,OAAA,CAACf,aAAa;UAAAuF,QAAA,eACZxE,OAAA;YACEkI,GAAG,EAAE,qBAAqBnH,aAAa,CAACyB,KAAK,EAAG;YAChD2E,GAAG,EAAC,gBAAgB;YACpBgB,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEnB,MAAM,EAAE,MAAM;cAAEoB,YAAY,EAAE;YAAM;UAAE;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC;MAAA,eAChB;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB;AAAClE,EAAA,CA1RuBD,YAAY;AAAAmI,EAAA,GAAZnI,YAAY;AAAA,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}