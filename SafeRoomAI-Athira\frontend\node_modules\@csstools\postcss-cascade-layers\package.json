{"name": "@csstools/postcss-cascade-layers", "description": "Use cascade layers in CSS", "version": "1.1.1", "contributors": [{"name": "<PERSON><PERSON>", "email": "o.ni<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON> J<PERSON>d", "email": "<EMAIL>"}, {"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "CC0-1.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^12 || ^14 || >=16"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"@csstools/selector-specificity": "^2.0.2", "postcss-selector-parser": "^6.0.10"}, "peerDependencies": {"postcss": "^8.2"}, "devDependencies": {"postcss-import": "^15.0.0", "puppeteer": "^17.1.3"}, "scripts": {"build": "rollup -c ../../rollup/default.js", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true });\"", "docs": "node ../../.github/bin/generate-docs/install.mjs && node ../../.github/bin/generate-docs/readme.mjs", "lint": "npm run lint:eslint && npm run lint:package-json", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "lint:package-json": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "test": "node .tape.mjs && npm run test:exports", "test:browser": "node ./test/_browser.mjs", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-cascade-layers#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-cascade-layers"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["cascade", "css", "layers", "postcss", "postcss-plugin", "selectors", "specificity"], "csstools": {"cssdbId": "cascade-layers", "exportName": "postcssCascadeLayers", "humanReadableName": "PostCSS Cascade Layers", "specUrl": "https://www.w3.org/TR/css-cascade-5/#layering"}, "volta": {"extends": "../../package.json"}}