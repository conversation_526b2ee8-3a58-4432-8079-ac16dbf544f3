{"ast": null, "code": "import { getDay } from \"./getDay.js\";\nimport { subDays } from \"./subDays.js\";\n\n/**\n * The {@link previousDay} function options.\n */\n\n/**\n * @name previousDay\n * @category Weekday Helpers\n * @summary When is the previous day of the week?\n *\n * @description\n * When is the previous day of the week? 0-6 the day of the week, 0 represents Sunday.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to check\n * @param day - The day of the week\n * @param options - An object with options\n *\n * @returns The date is the previous day of week\n *\n * @example\n * // When is the previous Monday before Mar, 20, 2020?\n * const result = previousDay(new Date(2020, 2, 20), 1)\n * //=> Mon Mar 16 2020 00:00:00\n *\n * @example\n * // When is the previous Tuesday before Mar, 21, 2020?\n * const result = previousDay(new Date(2020, 2, 21), 2)\n * //=> Tue Mar 17 2020 00:00:00\n */\nexport function previousDay(date, day, options) {\n  let delta = getDay(date, options) - day;\n  if (delta <= 0) delta += 7;\n  return subDays(date, delta, options);\n}\n\n// Fallback for modularized imports:\nexport default previousDay;", "map": {"version": 3, "names": ["getDay", "subDays", "previousDay", "date", "day", "options", "delta"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/capstone final project/SafeRoomAI/frontend/node_modules/date-fns/previousDay.js"], "sourcesContent": ["import { getDay } from \"./getDay.js\";\nimport { subDays } from \"./subDays.js\";\n\n/**\n * The {@link previousDay} function options.\n */\n\n/**\n * @name previousDay\n * @category Weekday Helpers\n * @summary When is the previous day of the week?\n *\n * @description\n * When is the previous day of the week? 0-6 the day of the week, 0 represents Sunday.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to check\n * @param day - The day of the week\n * @param options - An object with options\n *\n * @returns The date is the previous day of week\n *\n * @example\n * // When is the previous Monday before Mar, 20, 2020?\n * const result = previousDay(new Date(2020, 2, 20), 1)\n * //=> Mon Mar 16 2020 00:00:00\n *\n * @example\n * // When is the previous Tuesday before Mar, 21, 2020?\n * const result = previousDay(new Date(2020, 2, 21), 2)\n * //=> Tue Mar 17 2020 00:00:00\n */\nexport function previousDay(date, day, options) {\n  let delta = getDay(date, options) - day;\n  if (delta <= 0) delta += 7;\n\n  return subDays(date, delta, options);\n}\n\n// Fallback for modularized imports:\nexport default previousDay;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO,QAAQ,cAAc;;AAEtC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAE;EAC9C,IAAIC,KAAK,GAAGN,MAAM,CAACG,IAAI,EAAEE,OAAO,CAAC,GAAGD,GAAG;EACvC,IAAIE,KAAK,IAAI,CAAC,EAAEA,KAAK,IAAI,CAAC;EAE1B,OAAOL,OAAO,CAACE,IAAI,EAAEG,KAAK,EAAED,OAAO,CAAC;AACtC;;AAEA;AACA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}