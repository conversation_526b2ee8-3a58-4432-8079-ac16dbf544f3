{"ast": null, "code": "var _s = $RefreshSig$();\n// src/hooks/useFeedbackState.js\nimport { useState, useEffect } from 'react';\nimport feedbackService from '../services/feedbackService';\n\n/**\n * Custom hook for managing AI feedback state\n * Provides persistent feedback state management with localStorage backup\n */\nexport const useFeedbackState = (componentName = 'default') => {\n  _s();\n  const [feedbackStates, setFeedbackStates] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  // Load initial feedback states from localStorage\n  useEffect(() => {\n    const loadStoredFeedback = () => {\n      try {\n        const stored = localStorage.getItem(`ai_feedback_${componentName}`);\n        if (stored) {\n          const parsed = JSON.parse(stored);\n          setFeedbackStates(parsed);\n        }\n      } catch (error) {\n        console.error('Failed to load stored feedback:', error);\n      }\n    };\n    loadStoredFeedback();\n  }, [componentName]);\n\n  // Save feedback states to localStorage whenever they change\n  useEffect(() => {\n    try {\n      localStorage.setItem(`ai_feedback_${componentName}`, JSON.stringify(feedbackStates));\n    } catch (error) {\n      console.error('Failed to save feedback to localStorage:', error);\n    }\n  }, [feedbackStates, componentName]);\n\n  /**\n   * Submit feedback for a suggestion\n   */\n  const submitFeedback = async (suggestionId, feedbackType, suggestionType, metadata = {}) => {\n    setLoading(true);\n    try {\n      await feedbackService.submitFeedback(suggestionId, feedbackType, suggestionType, {\n        component: componentName,\n        ...metadata\n      });\n      setFeedbackStates(prev => ({\n        ...prev,\n        [suggestionId]: feedbackType\n      }));\n      return {\n        success: true\n      };\n    } catch (error) {\n      console.error('Failed to submit feedback:', error);\n      return {\n        success: false,\n        error\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Get feedback for a specific suggestion\n   */\n  const getFeedback = suggestionId => {\n    return feedbackStates[suggestionId] || null;\n  };\n\n  /**\n   * Check if feedback has been submitted for a suggestion\n   */\n  const hasFeedback = suggestionId => {\n    return suggestionId in feedbackStates;\n  };\n\n  /**\n   * Clear all feedback for this component\n   */\n  const clearFeedback = () => {\n    setFeedbackStates({});\n    localStorage.removeItem(`ai_feedback_${componentName}`);\n  };\n\n  /**\n   * Get feedback statistics for this component\n   */\n  const getFeedbackStats = () => {\n    const feedbackValues = Object.values(feedbackStates);\n    const total = feedbackValues.length;\n    const accepted = feedbackValues.filter(f => f === 'accept').length;\n    const rejected = feedbackValues.filter(f => f === 'reject').length;\n    return {\n      total,\n      accepted,\n      rejected,\n      acceptanceRate: total > 0 ? (accepted / total * 100).toFixed(1) : 0\n    };\n  };\n\n  /**\n   * Bulk load feedback from server (useful for initialization)\n   */\n  const loadFeedbackFromServer = async suggestionIds => {\n    setLoading(true);\n    try {\n      const promises = suggestionIds.map(id => feedbackService.getFeedback(id));\n      const results = await Promise.all(promises);\n      const serverFeedback = {};\n      results.forEach((result, index) => {\n        if (result && result.feedback_type) {\n          serverFeedback[suggestionIds[index]] = result.feedback_type;\n        }\n      });\n      setFeedbackStates(prev => ({\n        ...prev,\n        ...serverFeedback\n      }));\n      return {\n        success: true,\n        count: Object.keys(serverFeedback).length\n      };\n    } catch (error) {\n      console.error('Failed to load feedback from server:', error);\n      return {\n        success: false,\n        error\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n  return {\n    feedbackStates,\n    loading,\n    submitFeedback,\n    getFeedback,\n    hasFeedback,\n    clearFeedback,\n    getFeedbackStats,\n    loadFeedbackFromServer\n  };\n};\n_s(useFeedbackState, \"9P8lZ3USjYX3jHiPAs0MDoSTwCQ=\");\nexport default useFeedbackState;", "map": {"version": 3, "names": ["useState", "useEffect", "feedbackService", "useFeedbackState", "componentName", "_s", "feedbackStates", "setFeedbackStates", "loading", "setLoading", "loadStoredFeedback", "stored", "localStorage", "getItem", "parsed", "JSON", "parse", "error", "console", "setItem", "stringify", "submitFeedback", "suggestionId", "feedbackType", "suggestionType", "metadata", "component", "prev", "success", "getFeedback", "hasFeedback", "clearFeedback", "removeItem", "getFeedbackStats", "feedbackV<PERSON>ues", "Object", "values", "total", "length", "accepted", "filter", "f", "rejected", "acceptanceRate", "toFixed", "loadFeedbackFromServer", "suggestionIds", "promises", "map", "id", "results", "Promise", "all", "serverFeedback", "for<PERSON>ach", "result", "index", "feedback_type", "count", "keys"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/capstone final project/SafeRoomAI-Athira/frontend/src/hooks/useFeedbackState.js"], "sourcesContent": ["// src/hooks/useFeedbackState.js\nimport { useState, useEffect } from 'react';\nimport feedbackService from '../services/feedbackService';\n\n/**\n * Custom hook for managing AI feedback state\n * Provides persistent feedback state management with localStorage backup\n */\nexport const useFeedbackState = (componentName = 'default') => {\n  const [feedbackStates, setFeedbackStates] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  // Load initial feedback states from localStorage\n  useEffect(() => {\n    const loadStoredFeedback = () => {\n      try {\n        const stored = localStorage.getItem(`ai_feedback_${componentName}`);\n        if (stored) {\n          const parsed = JSON.parse(stored);\n          setFeedbackStates(parsed);\n        }\n      } catch (error) {\n        console.error('Failed to load stored feedback:', error);\n      }\n    };\n\n    loadStoredFeedback();\n  }, [componentName]);\n\n  // Save feedback states to localStorage whenever they change\n  useEffect(() => {\n    try {\n      localStorage.setItem(\n        `ai_feedback_${componentName}`, \n        JSON.stringify(feedbackStates)\n      );\n    } catch (error) {\n      console.error('Failed to save feedback to localStorage:', error);\n    }\n  }, [feedbackStates, componentName]);\n\n  /**\n   * Submit feedback for a suggestion\n   */\n  const submitFeedback = async (suggestionId, feedbackType, suggestionType, metadata = {}) => {\n    setLoading(true);\n    try {\n      await feedbackService.submitFeedback(suggestionId, feedbackType, suggestionType, {\n        component: componentName,\n        ...metadata\n      });\n      \n      setFeedbackStates(prev => ({\n        ...prev,\n        [suggestionId]: feedbackType\n      }));\n\n      return { success: true };\n    } catch (error) {\n      console.error('Failed to submit feedback:', error);\n      return { success: false, error };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Get feedback for a specific suggestion\n   */\n  const getFeedback = (suggestionId) => {\n    return feedbackStates[suggestionId] || null;\n  };\n\n  /**\n   * Check if feedback has been submitted for a suggestion\n   */\n  const hasFeedback = (suggestionId) => {\n    return suggestionId in feedbackStates;\n  };\n\n  /**\n   * Clear all feedback for this component\n   */\n  const clearFeedback = () => {\n    setFeedbackStates({});\n    localStorage.removeItem(`ai_feedback_${componentName}`);\n  };\n\n  /**\n   * Get feedback statistics for this component\n   */\n  const getFeedbackStats = () => {\n    const feedbackValues = Object.values(feedbackStates);\n    const total = feedbackValues.length;\n    const accepted = feedbackValues.filter(f => f === 'accept').length;\n    const rejected = feedbackValues.filter(f => f === 'reject').length;\n    \n    return {\n      total,\n      accepted,\n      rejected,\n      acceptanceRate: total > 0 ? (accepted / total * 100).toFixed(1) : 0\n    };\n  };\n\n  /**\n   * Bulk load feedback from server (useful for initialization)\n   */\n  const loadFeedbackFromServer = async (suggestionIds) => {\n    setLoading(true);\n    try {\n      const promises = suggestionIds.map(id => feedbackService.getFeedback(id));\n      const results = await Promise.all(promises);\n      \n      const serverFeedback = {};\n      results.forEach((result, index) => {\n        if (result && result.feedback_type) {\n          serverFeedback[suggestionIds[index]] = result.feedback_type;\n        }\n      });\n\n      setFeedbackStates(prev => ({\n        ...prev,\n        ...serverFeedback\n      }));\n\n      return { success: true, count: Object.keys(serverFeedback).length };\n    } catch (error) {\n      console.error('Failed to load feedback from server:', error);\n      return { success: false, error };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return {\n    feedbackStates,\n    loading,\n    submitFeedback,\n    getFeedback,\n    hasFeedback,\n    clearFeedback,\n    getFeedbackStats,\n    loadFeedbackFromServer\n  };\n};\n\nexport default useFeedbackState;\n"], "mappings": ";AAAA;AACA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,eAAe,MAAM,6BAA6B;;AAEzD;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,GAAGA,CAACC,aAAa,GAAG,SAAS,KAAK;EAAAC,EAAA;EAC7D,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGP,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMS,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,IAAI;QACF,MAAMC,MAAM,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAeT,aAAa,EAAE,CAAC;QACnE,IAAIO,MAAM,EAAE;UACV,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,MAAM,CAAC;UACjCJ,iBAAiB,CAACO,MAAM,CAAC;QAC3B;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;IACF,CAAC;IAEDP,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACN,aAAa,CAAC,CAAC;;EAEnB;EACAH,SAAS,CAAC,MAAM;IACd,IAAI;MACFW,YAAY,CAACO,OAAO,CAClB,eAAef,aAAa,EAAE,EAC9BW,IAAI,CAACK,SAAS,CAACd,cAAc,CAC/B,CAAC;IACH,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;IAClE;EACF,CAAC,EAAE,CAACX,cAAc,EAAEF,aAAa,CAAC,CAAC;;EAEnC;AACF;AACA;EACE,MAAMiB,cAAc,GAAG,MAAAA,CAAOC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,QAAQ,GAAG,CAAC,CAAC,KAAK;IAC1FhB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMP,eAAe,CAACmB,cAAc,CAACC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAE;QAC/EE,SAAS,EAAEtB,aAAa;QACxB,GAAGqB;MACL,CAAC,CAAC;MAEFlB,iBAAiB,CAACoB,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP,CAACL,YAAY,GAAGC;MAClB,CAAC,CAAC,CAAC;MAEH,OAAO;QAAEK,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO;QAAEW,OAAO,EAAE,KAAK;QAAEX;MAAM,CAAC;IAClC,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMoB,WAAW,GAAIP,YAAY,IAAK;IACpC,OAAOhB,cAAc,CAACgB,YAAY,CAAC,IAAI,IAAI;EAC7C,CAAC;;EAED;AACF;AACA;EACE,MAAMQ,WAAW,GAAIR,YAAY,IAAK;IACpC,OAAOA,YAAY,IAAIhB,cAAc;EACvC,CAAC;;EAED;AACF;AACA;EACE,MAAMyB,aAAa,GAAGA,CAAA,KAAM;IAC1BxB,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACrBK,YAAY,CAACoB,UAAU,CAAC,eAAe5B,aAAa,EAAE,CAAC;EACzD,CAAC;;EAED;AACF;AACA;EACE,MAAM6B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,cAAc,GAAGC,MAAM,CAACC,MAAM,CAAC9B,cAAc,CAAC;IACpD,MAAM+B,KAAK,GAAGH,cAAc,CAACI,MAAM;IACnC,MAAMC,QAAQ,GAAGL,cAAc,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK,QAAQ,CAAC,CAACH,MAAM;IAClE,MAAMI,QAAQ,GAAGR,cAAc,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK,QAAQ,CAAC,CAACH,MAAM;IAElE,OAAO;MACLD,KAAK;MACLE,QAAQ;MACRG,QAAQ;MACRC,cAAc,EAAEN,KAAK,GAAG,CAAC,GAAG,CAACE,QAAQ,GAAGF,KAAK,GAAG,GAAG,EAAEO,OAAO,CAAC,CAAC,CAAC,GAAG;IACpE,CAAC;EACH,CAAC;;EAED;AACF;AACA;EACE,MAAMC,sBAAsB,GAAG,MAAOC,aAAa,IAAK;IACtDrC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMsC,QAAQ,GAAGD,aAAa,CAACE,GAAG,CAACC,EAAE,IAAI/C,eAAe,CAAC2B,WAAW,CAACoB,EAAE,CAAC,CAAC;MACzE,MAAMC,OAAO,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC;MAE3C,MAAMM,cAAc,GAAG,CAAC,CAAC;MACzBH,OAAO,CAACI,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;QACjC,IAAID,MAAM,IAAIA,MAAM,CAACE,aAAa,EAAE;UAClCJ,cAAc,CAACP,aAAa,CAACU,KAAK,CAAC,CAAC,GAAGD,MAAM,CAACE,aAAa;QAC7D;MACF,CAAC,CAAC;MAEFlD,iBAAiB,CAACoB,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP,GAAG0B;MACL,CAAC,CAAC,CAAC;MAEH,OAAO;QAAEzB,OAAO,EAAE,IAAI;QAAE8B,KAAK,EAAEvB,MAAM,CAACwB,IAAI,CAACN,cAAc,CAAC,CAACf;MAAO,CAAC;IACrE,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO;QAAEW,OAAO,EAAE,KAAK;QAAEX;MAAM,CAAC;IAClC,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,OAAO;IACLH,cAAc;IACdE,OAAO;IACPa,cAAc;IACdQ,WAAW;IACXC,WAAW;IACXC,aAAa;IACbE,gBAAgB;IAChBY;EACF,CAAC;AACH,CAAC;AAACxC,EAAA,CAzIWF,gBAAgB;AA2I7B,eAAeA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}