"use client";

import createSvgIcon from "./utils/createSvgIcon.js";
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("path", {
  d: "m4.94 7.26 2.21 2.21c1.44-.91 3.11-1.4 4.85-1.4s3.41.49 4.84 1.4l2.21-2.21C17 5.79 14.56 5 12 5s-5.01.79-7.06 2.26",
  opacity: ".3"
}, "0"), /*#__PURE__*/_jsx("circle", {
  cx: "12",
  cy: "17",
  r: "3",
  opacity: ".3"
}, "1"), /*#__PURE__*/_jsx("path", {
  d: "M17 17c0-2.76-2.24-5-5-5s-5 2.24-5 5 2.24 5 5 5 5-2.24 5-5m-8 0c0-1.65 1.35-3 3-3s3 1.35 3 3-1.35 3-3 3-3-1.35-3-3M2 7.15l5 5c1.28-1.28 3.05-2.08 5-2.08s3.72.79 5 2.07l5-5C19.44 4.59 15.9 3 12 3S4.56 4.59 2 7.15m14.84 2.32c-1.44-.91-3.1-1.4-4.84-1.4s-3.41.49-4.85 1.41L4.94 7.26C6.99 5.79 9.44 5 12 5s5 .79 7.05 2.26z"
}, "2")], 'CompassCalibrationTwoTone');