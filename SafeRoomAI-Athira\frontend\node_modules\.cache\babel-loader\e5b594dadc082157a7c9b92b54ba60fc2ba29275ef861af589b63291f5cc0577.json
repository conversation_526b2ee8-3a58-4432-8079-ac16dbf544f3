{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\capstone final project\\\\SafeRoomAI-Athira\\\\frontend\\\\src\\\\components\\\\anomalysnapshots.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport AIFeedbackButtons from './AIFeedbackButtons';\nimport feedbackService from '../services/feedbackService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnomalySnapshots = () => {\n  _s();\n  const [snapshots, setSnapshots] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [feedbackStates, setFeedbackStates] = useState({});\n  useEffect(() => {\n    const fetchSnapshots = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch('/predict/analytics/snapshots');\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}`);\n        }\n        const data = await response.json();\n        setSnapshots(data);\n        setError(null);\n      } catch (err) {\n        console.error('Failed to fetch snapshots data:', err);\n        setError('Failed to load data');\n        // Fallback to mock data if API fails\n        const now = new Date();\n        const mockSnapshots = [{\n          id: 1,\n          time: new Date(now.getTime() - 15 * 60000).toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n          }),\n          img: '/static.png',\n          confidence: 0.85,\n          type: 'Motion Detection'\n        }, {\n          id: 2,\n          time: new Date(now.getTime() - 8 * 60000).toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n          }),\n          img: '/static.png',\n          confidence: 0.92,\n          type: 'Object Recognition'\n        }, {\n          id: 3,\n          time: new Date(now.getTime() - 2 * 60000).toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n          }),\n          img: '/static.png',\n          confidence: 0.78,\n          type: 'Unusual Activity'\n        }];\n        setSnapshots(mockSnapshots);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchSnapshots();\n\n    // Set up polling for real-time updates every 45 seconds\n    const interval = setInterval(fetchSnapshots, 45000);\n    return () => clearInterval(interval);\n  }, []);\n  const handleImageError = e => {\n    e.target.src = '/static.png'; // Fallback to existing static image\n  };\n  const getConfidenceColor = confidence => {\n    if (confidence >= 0.8) return '#10b981'; // green\n    if (confidence >= 0.6) return '#f59e0b'; // yellow\n    return '#ef4444'; // red\n  };\n  const handleFeedback = async (suggestionId, feedbackType, suggestionType) => {\n    try {\n      var _snapshots$find, _snapshots$find2;\n      await feedbackService.submitFeedback(suggestionId, feedbackType, suggestionType, {\n        confidence: (_snapshots$find = snapshots.find(s => s.id === suggestionId)) === null || _snapshots$find === void 0 ? void 0 : _snapshots$find.confidence,\n        anomaly_type: (_snapshots$find2 = snapshots.find(s => s.id === suggestionId)) === null || _snapshots$find2 === void 0 ? void 0 : _snapshots$find2.type\n      });\n      setFeedbackStates(prev => ({\n        ...prev,\n        [suggestionId]: feedbackType\n      }));\n    } catch (error) {\n      console.error('Failed to submit feedback:', error);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-loading\",\n      children: \"Loading snapshots...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 12\n    }, this);\n  }\n  if (snapshots.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-snapshots\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No recent anomaly snapshots available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"anomaly-snapshots-container\",\n    children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-error\",\n      children: [\"\\u26A0\\uFE0F \", error, \" (showing fallback data)\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"snapshots-grid\",\n      children: snapshots.map(snap => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"snapshot-item\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"snapshot-image-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: snap.img || snap.image_url || '/static.png',\n            alt: `Anomaly at ${snap.time}`,\n            className: \"snapshot-image\",\n            onError: handleImageError,\n            style: {\n              width: '150px',\n              height: '100px',\n              objectFit: 'cover',\n              borderRadius: '8px',\n              boxShadow: feedbackStates[snap.id] === 'accept' ? '0 2px 8px rgba(16, 185, 129, 0.3)' : feedbackStates[snap.id] === 'reject' ? '0 2px 8px rgba(239, 68, 68, 0.3)' : '0 2px 8px rgba(0,0,0,0.1)',\n              border: feedbackStates[snap.id] === 'accept' ? '2px solid #10b981' : feedbackStates[snap.id] === 'reject' ? '2px solid #ef4444' : '2px solid #e5e7eb',\n              transition: 'all 0.3s ease'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this), snap.confidence && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"confidence-badge\",\n            style: {\n              position: 'absolute',\n              top: '4px',\n              right: '4px',\n              backgroundColor: getConfidenceColor(snap.confidence),\n              color: 'white',\n              padding: '2px 6px',\n              borderRadius: '12px',\n              fontSize: '10px',\n              fontWeight: 'bold'\n            },\n            children: [Math.round(snap.confidence * 100), \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 17\n          }, this), feedbackStates[snap.id] && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: '4px',\n              left: '4px',\n              backgroundColor: feedbackStates[snap.id] === 'accept' ? '#10b981' : '#ef4444',\n              color: 'white',\n              padding: '2px 4px',\n              borderRadius: '8px',\n              fontSize: '8px',\n              fontWeight: 'bold',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '2px'\n            },\n            children: [feedbackStates[snap.id] === 'accept' ? '✓' : '✗', feedbackStates[snap.id] === 'accept' ? 'Helpful' : 'Not helpful']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"snapshot-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"snapshot-time\",\n            style: {\n              fontSize: '12px',\n              fontWeight: 'bold',\n              marginTop: '4px'\n            },\n            children: snap.time\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this), snap.type && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"snapshot-type\",\n            style: {\n              fontSize: '10px',\n              color: '#6b7280',\n              marginTop: '2px'\n            },\n            children: snap.type\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '8px'\n            },\n            children: /*#__PURE__*/_jsxDEV(AIFeedbackButtons, {\n              suggestionId: snap.id,\n              suggestionType: \"anomaly_detection\",\n              onFeedback: handleFeedback,\n              variant: \"compact\",\n              initialFeedback: feedbackStates[snap.id]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this)]\n      }, snap.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n};\n_s(AnomalySnapshots, \"9y4f3KDL7uQ99inwZkhk3EWJvVQ=\");\n_c = AnomalySnapshots;\nexport default AnomalySnapshots;\nvar _c;\n$RefreshReg$(_c, \"AnomalySnapshots\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AIFeedbackButtons", "feedbackService", "jsxDEV", "_jsxDEV", "AnomalySnapshots", "_s", "snapshots", "setSnapshots", "loading", "setLoading", "error", "setError", "feedbackStates", "setFeedbackStates", "fetchSnapshots", "response", "fetch", "ok", "Error", "status", "data", "json", "err", "console", "now", "Date", "mockSnapshots", "id", "time", "getTime", "toLocaleTimeString", "hour", "minute", "img", "confidence", "type", "interval", "setInterval", "clearInterval", "handleImageError", "e", "target", "src", "getConfidenceColor", "handleFeedback", "suggestionId", "feedbackType", "suggestionType", "_snapshots$find", "_snapshots$find2", "submitFeedback", "find", "s", "anomaly_type", "prev", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "snap", "image_url", "alt", "onError", "style", "width", "height", "objectFit", "borderRadius", "boxShadow", "border", "transition", "position", "top", "right", "backgroundColor", "color", "padding", "fontSize", "fontWeight", "Math", "round", "left", "display", "alignItems", "gap", "marginTop", "onFeedback", "variant", "initialFeedback", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/capstone final project/SafeRoomAI-Athira/frontend/src/components/anomalysnapshots.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport AIFeedbackButtons from './AIFeedbackButtons';\r\nimport feedbackService from '../services/feedbackService';\r\n\r\nconst AnomalySnapshots = () => {\r\n  const [snapshots, setSnapshots] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [feedbackStates, setFeedbackStates] = useState({});\r\n\r\n  useEffect(() => {\r\n    const fetchSnapshots = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await fetch('/predict/analytics/snapshots');\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        setSnapshots(data);\r\n        setError(null);\r\n      } catch (err) {\r\n        console.error('Failed to fetch snapshots data:', err);\r\n        setError('Failed to load data');\r\n        // Fallback to mock data if API fails\r\n        const now = new Date();\r\n        const mockSnapshots = [\r\n          {\r\n            id: 1,\r\n            time: new Date(now.getTime() - 15 * 60000).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),\r\n            img: '/static.png',\r\n            confidence: 0.85,\r\n            type: 'Motion Detection'\r\n          },\r\n          {\r\n            id: 2,\r\n            time: new Date(now.getTime() - 8 * 60000).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),\r\n            img: '/static.png',\r\n            confidence: 0.92,\r\n            type: 'Object Recognition'\r\n          },\r\n          {\r\n            id: 3,\r\n            time: new Date(now.getTime() - 2 * 60000).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),\r\n            img: '/static.png',\r\n            confidence: 0.78,\r\n            type: 'Unusual Activity'\r\n          },\r\n        ];\r\n        setSnapshots(mockSnapshots);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchSnapshots();\r\n\r\n    // Set up polling for real-time updates every 45 seconds\r\n    const interval = setInterval(fetchSnapshots, 45000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  const handleImageError = (e) => {\r\n    e.target.src = '/static.png'; // Fallback to existing static image\r\n  };\r\n\r\n  const getConfidenceColor = (confidence) => {\r\n    if (confidence >= 0.8) return '#10b981'; // green\r\n    if (confidence >= 0.6) return '#f59e0b'; // yellow\r\n    return '#ef4444'; // red\r\n  };\r\n\r\n  const handleFeedback = async (suggestionId, feedbackType, suggestionType) => {\r\n    try {\r\n      await feedbackService.submitFeedback(suggestionId, feedbackType, suggestionType, {\r\n        confidence: snapshots.find(s => s.id === suggestionId)?.confidence,\r\n        anomaly_type: snapshots.find(s => s.id === suggestionId)?.type\r\n      });\r\n\r\n      setFeedbackStates(prev => ({\r\n        ...prev,\r\n        [suggestionId]: feedbackType\r\n      }));\r\n    } catch (error) {\r\n      console.error('Failed to submit feedback:', error);\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return <div className=\"chart-loading\">Loading snapshots...</div>;\r\n  }\r\n\r\n  if (snapshots.length === 0) {\r\n    return (\r\n      <div className=\"no-snapshots\">\r\n        <p>No recent anomaly snapshots available</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"anomaly-snapshots-container\">\r\n      {error && <div className=\"chart-error\">⚠️ {error} (showing fallback data)</div>}\r\n      <div className=\"snapshots-grid\">\r\n        {snapshots.map((snap) => (\r\n          <div key={snap.id} className=\"snapshot-item\">\r\n            <div className=\"snapshot-image-container\">\r\n              <img\r\n                src={snap.img || snap.image_url || '/static.png'}\r\n                alt={`Anomaly at ${snap.time}`}\r\n                className=\"snapshot-image\"\r\n                onError={handleImageError}\r\n                style={{\r\n                  width: '150px',\r\n                  height: '100px',\r\n                  objectFit: 'cover',\r\n                  borderRadius: '8px',\r\n                  boxShadow: feedbackStates[snap.id] === 'accept'\r\n                    ? '0 2px 8px rgba(16, 185, 129, 0.3)'\r\n                    : feedbackStates[snap.id] === 'reject'\r\n                    ? '0 2px 8px rgba(239, 68, 68, 0.3)'\r\n                    : '0 2px 8px rgba(0,0,0,0.1)',\r\n                  border: feedbackStates[snap.id] === 'accept'\r\n                    ? '2px solid #10b981'\r\n                    : feedbackStates[snap.id] === 'reject'\r\n                    ? '2px solid #ef4444'\r\n                    : '2px solid #e5e7eb',\r\n                  transition: 'all 0.3s ease'\r\n                }}\r\n              />\r\n              {snap.confidence && (\r\n                <div\r\n                  className=\"confidence-badge\"\r\n                  style={{\r\n                    position: 'absolute',\r\n                    top: '4px',\r\n                    right: '4px',\r\n                    backgroundColor: getConfidenceColor(snap.confidence),\r\n                    color: 'white',\r\n                    padding: '2px 6px',\r\n                    borderRadius: '12px',\r\n                    fontSize: '10px',\r\n                    fontWeight: 'bold'\r\n                  }}\r\n                >\r\n                  {Math.round(snap.confidence * 100)}%\r\n                </div>\r\n              )}\r\n\r\n              {/* Feedback status indicator */}\r\n              {feedbackStates[snap.id] && (\r\n                <div\r\n                  style={{\r\n                    position: 'absolute',\r\n                    top: '4px',\r\n                    left: '4px',\r\n                    backgroundColor: feedbackStates[snap.id] === 'accept' ? '#10b981' : '#ef4444',\r\n                    color: 'white',\r\n                    padding: '2px 4px',\r\n                    borderRadius: '8px',\r\n                    fontSize: '8px',\r\n                    fontWeight: 'bold',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    gap: '2px'\r\n                  }}\r\n                >\r\n                  {feedbackStates[snap.id] === 'accept' ? '✓' : '✗'}\r\n                  {feedbackStates[snap.id] === 'accept' ? 'Helpful' : 'Not helpful'}\r\n                </div>\r\n              )}\r\n            </div>\r\n            <div className=\"snapshot-info\">\r\n              <div className=\"snapshot-time\" style={{fontSize: '12px', fontWeight: 'bold', marginTop: '4px'}}>\r\n                {snap.time}\r\n              </div>\r\n              {snap.type && (\r\n                <div className=\"snapshot-type\" style={{fontSize: '10px', color: '#6b7280', marginTop: '2px'}}>\r\n                  {snap.type}\r\n                </div>\r\n              )}\r\n\r\n              {/* AI Feedback Buttons */}\r\n              <div style={{ marginTop: '8px' }}>\r\n                <AIFeedbackButtons\r\n                  suggestionId={snap.id}\r\n                  suggestionType=\"anomaly_detection\"\r\n                  onFeedback={handleFeedback}\r\n                  variant=\"compact\"\r\n                  initialFeedback={feedbackStates[snap.id]}\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AnomalySnapshots;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,eAAe,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExDC,SAAS,CAAC,MAAM;IACd,MAAMe,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFL,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,8BAA8B,CAAC;QAE5D,IAAI,CAACD,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,QAAQH,QAAQ,CAACI,MAAM,EAAE,CAAC;QAC5C;QAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCd,YAAY,CAACa,IAAI,CAAC;QAClBT,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOW,GAAG,EAAE;QACZC,OAAO,CAACb,KAAK,CAAC,iCAAiC,EAAEY,GAAG,CAAC;QACrDX,QAAQ,CAAC,qBAAqB,CAAC;QAC/B;QACA,MAAMa,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtB,MAAMC,aAAa,GAAG,CACpB;UACEC,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,IAAIH,IAAI,CAACD,GAAG,CAACK,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;YAACC,IAAI,EAAE,SAAS;YAAEC,MAAM,EAAC;UAAS,CAAC,CAAC;UACtGC,GAAG,EAAE,aAAa;UAClBC,UAAU,EAAE,IAAI;UAChBC,IAAI,EAAE;QACR,CAAC,EACD;UACER,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,IAAIH,IAAI,CAACD,GAAG,CAACK,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;YAACC,IAAI,EAAE,SAAS;YAAEC,MAAM,EAAC;UAAS,CAAC,CAAC;UACrGC,GAAG,EAAE,aAAa;UAClBC,UAAU,EAAE,IAAI;UAChBC,IAAI,EAAE;QACR,CAAC,EACD;UACER,EAAE,EAAE,CAAC;UACLC,IAAI,EAAE,IAAIH,IAAI,CAACD,GAAG,CAACK,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;YAACC,IAAI,EAAE,SAAS;YAAEC,MAAM,EAAC;UAAS,CAAC,CAAC;UACrGC,GAAG,EAAE,aAAa;UAClBC,UAAU,EAAE,IAAI;UAChBC,IAAI,EAAE;QACR,CAAC,CACF;QACD5B,YAAY,CAACmB,aAAa,CAAC;MAC7B,CAAC,SAAS;QACRjB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDK,cAAc,CAAC,CAAC;;IAEhB;IACA,MAAMsB,QAAQ,GAAGC,WAAW,CAACvB,cAAc,EAAE,KAAK,CAAC;IAEnD,OAAO,MAAMwB,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,gBAAgB,GAAIC,CAAC,IAAK;IAC9BA,CAAC,CAACC,MAAM,CAACC,GAAG,GAAG,aAAa,CAAC,CAAC;EAChC,CAAC;EAED,MAAMC,kBAAkB,GAAIT,UAAU,IAAK;IACzC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS,CAAC,CAAC;IACzC,IAAIA,UAAU,IAAI,GAAG,EAAE,OAAO,SAAS,CAAC,CAAC;IACzC,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;EAED,MAAMU,cAAc,GAAG,MAAAA,CAAOC,YAAY,EAAEC,YAAY,EAAEC,cAAc,KAAK;IAC3E,IAAI;MAAA,IAAAC,eAAA,EAAAC,gBAAA;MACF,MAAMhD,eAAe,CAACiD,cAAc,CAACL,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAE;QAC/Eb,UAAU,GAAAc,eAAA,GAAE1C,SAAS,CAAC6C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzB,EAAE,KAAKkB,YAAY,CAAC,cAAAG,eAAA,uBAA1CA,eAAA,CAA4Cd,UAAU;QAClEmB,YAAY,GAAAJ,gBAAA,GAAE3C,SAAS,CAAC6C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzB,EAAE,KAAKkB,YAAY,CAAC,cAAAI,gBAAA,uBAA1CA,gBAAA,CAA4Cd;MAC5D,CAAC,CAAC;MAEFtB,iBAAiB,CAACyC,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP,CAACT,YAAY,GAAGC;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;EAED,IAAIF,OAAO,EAAE;IACX,oBAAOL,OAAA;MAAKoD,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAClE;EAEA,IAAItD,SAAS,CAACuD,MAAM,KAAK,CAAC,EAAE;IAC1B,oBACE1D,OAAA;MAAKoD,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BrD,OAAA;QAAAqD,QAAA,EAAG;MAAqC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC;EAEV;EAEA,oBACEzD,OAAA;IAAKoD,SAAS,EAAC,6BAA6B;IAAAC,QAAA,GACzC9C,KAAK,iBAAIP,OAAA;MAAKoD,SAAS,EAAC,aAAa;MAAAC,QAAA,GAAC,eAAG,EAAC9C,KAAK,EAAC,0BAAwB;IAAA;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAC/EzD,OAAA;MAAKoD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC5BlD,SAAS,CAACwD,GAAG,CAAEC,IAAI,iBAClB5D,OAAA;QAAmBoD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1CrD,OAAA;UAAKoD,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCrD,OAAA;YACEuC,GAAG,EAAEqB,IAAI,CAAC9B,GAAG,IAAI8B,IAAI,CAACC,SAAS,IAAI,aAAc;YACjDC,GAAG,EAAE,cAAcF,IAAI,CAACnC,IAAI,EAAG;YAC/B2B,SAAS,EAAC,gBAAgB;YAC1BW,OAAO,EAAE3B,gBAAiB;YAC1B4B,KAAK,EAAE;cACLC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,OAAO;cACfC,SAAS,EAAE,OAAO;cAClBC,YAAY,EAAE,KAAK;cACnBC,SAAS,EAAE5D,cAAc,CAACmD,IAAI,CAACpC,EAAE,CAAC,KAAK,QAAQ,GAC3C,mCAAmC,GACnCf,cAAc,CAACmD,IAAI,CAACpC,EAAE,CAAC,KAAK,QAAQ,GACpC,kCAAkC,GAClC,2BAA2B;cAC/B8C,MAAM,EAAE7D,cAAc,CAACmD,IAAI,CAACpC,EAAE,CAAC,KAAK,QAAQ,GACxC,mBAAmB,GACnBf,cAAc,CAACmD,IAAI,CAACpC,EAAE,CAAC,KAAK,QAAQ,GACpC,mBAAmB,GACnB,mBAAmB;cACvB+C,UAAU,EAAE;YACd;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACDG,IAAI,CAAC7B,UAAU,iBACd/B,OAAA;YACEoD,SAAS,EAAC,kBAAkB;YAC5BY,KAAK,EAAE;cACLQ,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,KAAK;cACVC,KAAK,EAAE,KAAK;cACZC,eAAe,EAAEnC,kBAAkB,CAACoB,IAAI,CAAC7B,UAAU,CAAC;cACpD6C,KAAK,EAAE,OAAO;cACdC,OAAO,EAAE,SAAS;cAClBT,YAAY,EAAE,MAAM;cACpBU,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAA1B,QAAA,GAED2B,IAAI,CAACC,KAAK,CAACrB,IAAI,CAAC7B,UAAU,GAAG,GAAG,CAAC,EAAC,GACrC;UAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN,EAGAhD,cAAc,CAACmD,IAAI,CAACpC,EAAE,CAAC,iBACtBxB,OAAA;YACEgE,KAAK,EAAE;cACLQ,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,KAAK;cACVS,IAAI,EAAE,KAAK;cACXP,eAAe,EAAElE,cAAc,CAACmD,IAAI,CAACpC,EAAE,CAAC,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;cAC7EoD,KAAK,EAAE,OAAO;cACdC,OAAO,EAAE,SAAS;cAClBT,YAAY,EAAE,KAAK;cACnBU,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE,MAAM;cAClBI,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAhC,QAAA,GAED5C,cAAc,CAACmD,IAAI,CAACpC,EAAE,CAAC,KAAK,QAAQ,GAAG,GAAG,GAAG,GAAG,EAChDf,cAAc,CAACmD,IAAI,CAACpC,EAAE,CAAC,KAAK,QAAQ,GAAG,SAAS,GAAG,aAAa;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNzD,OAAA;UAAKoD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BrD,OAAA;YAAKoD,SAAS,EAAC,eAAe;YAACY,KAAK,EAAE;cAACc,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEO,SAAS,EAAE;YAAK,CAAE;YAAAjC,QAAA,EAC5FO,IAAI,CAACnC;UAAI;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,EACLG,IAAI,CAAC5B,IAAI,iBACRhC,OAAA;YAAKoD,SAAS,EAAC,eAAe;YAACY,KAAK,EAAE;cAACc,QAAQ,EAAE,MAAM;cAAEF,KAAK,EAAE,SAAS;cAAEU,SAAS,EAAE;YAAK,CAAE;YAAAjC,QAAA,EAC1FO,IAAI,CAAC5B;UAAI;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CACN,eAGDzD,OAAA;YAAKgE,KAAK,EAAE;cAAEsB,SAAS,EAAE;YAAM,CAAE;YAAAjC,QAAA,eAC/BrD,OAAA,CAACH,iBAAiB;cAChB6C,YAAY,EAAEkB,IAAI,CAACpC,EAAG;cACtBoB,cAAc,EAAC,mBAAmB;cAClC2C,UAAU,EAAE9C,cAAe;cAC3B+C,OAAO,EAAC,SAAS;cACjBC,eAAe,EAAEhF,cAAc,CAACmD,IAAI,CAACpC,EAAE;YAAE;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GAvFEG,IAAI,CAACpC,EAAE;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwFZ,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvD,EAAA,CArMID,gBAAgB;AAAAyF,EAAA,GAAhBzF,gBAAgB;AAuMtB,eAAeA,gBAAgB;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}