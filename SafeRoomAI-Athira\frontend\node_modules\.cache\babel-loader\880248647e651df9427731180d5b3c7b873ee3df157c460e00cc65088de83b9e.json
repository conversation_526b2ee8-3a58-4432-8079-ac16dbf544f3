{"ast": null, "code": "// src/services/feedbackService.js\n\n/**\n * Service for handling AI feedback submissions\n */\nclass FeedbackService {\n  constructor() {\n    this.baseUrl = '/api/feedback';\n    this.cache = new Map(); // Cache feedback to prevent duplicate submissions\n  }\n\n  /**\n   * Submit feedback for an AI suggestion\n   * @param {string} suggestionId - Unique identifier for the suggestion\n   * @param {string} feedbackType - 'accept' or 'reject'\n   * @param {string} suggestionType - Type of suggestion (e.g., 'anomaly', 'alert', 'recommendation')\n   * @param {Object} metadata - Additional context about the suggestion\n   * @returns {Promise<Object>} Response from the server\n   */\n  async submitFeedback(suggestionId, feedbackType, suggestionType, metadata = {}) {\n    const cacheKey = `${suggestionId}_${feedbackType}`;\n\n    // Check if feedback already submitted\n    if (this.cache.has(cacheKey)) {\n      console.log('Feedback already submitted for this suggestion');\n      return this.cache.get(cacheKey);\n    }\n    const payload = {\n      suggestion_id: suggestionId,\n      feedback_type: feedbackType,\n      suggestion_type: suggestionType,\n      timestamp: new Date().toISOString(),\n      metadata: {\n        user_agent: navigator.userAgent,\n        screen_resolution: `${window.screen.width}x${window.screen.height}`,\n        ...metadata\n      }\n    };\n    try {\n      const response = await fetch(`${this.baseUrl}/submit`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(payload)\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n      const result = await response.json();\n\n      // Cache the successful response\n      this.cache.set(cacheKey, result);\n\n      // Log for analytics\n      this.logFeedbackEvent(suggestionId, feedbackType, suggestionType);\n      return result;\n    } catch (error) {\n      console.error('Failed to submit feedback:', error);\n\n      // For now, simulate success to avoid blocking the UI\n      // In production, you might want to queue failed requests for retry\n      const fallbackResult = {\n        success: true,\n        message: 'Feedback recorded locally',\n        suggestion_id: suggestionId,\n        feedback_type: feedbackType\n      };\n      this.cache.set(cacheKey, fallbackResult);\n      return fallbackResult;\n    }\n  }\n\n  /**\n   * Get feedback statistics for analytics\n   * @returns {Promise<Object>} Feedback statistics\n   */\n  async getFeedbackStats() {\n    try {\n      const response = await fetch(`${this.baseUrl}/stats`);\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error('Failed to fetch feedback stats:', error);\n      return {\n        total_feedback: 0,\n        accepted: 0,\n        rejected: 0,\n        acceptance_rate: 0\n      };\n    }\n  }\n\n  /**\n   * Get feedback for a specific suggestion\n   * @param {string} suggestionId - Suggestion identifier\n   * @returns {Promise<Object|null>} Existing feedback or null\n   */\n  async getFeedback(suggestionId) {\n    try {\n      const response = await fetch(`${this.baseUrl}/${suggestionId}`);\n      if (response.status === 404) {\n        return null;\n      }\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error('Failed to fetch feedback:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Log feedback event for analytics\n   * @private\n   */\n  logFeedbackEvent(suggestionId, feedbackType, suggestionType) {\n    // Log to console for development\n    console.log('Feedback Event:', {\n      suggestion_id: suggestionId,\n      feedback_type: feedbackType,\n      suggestion_type: suggestionType,\n      timestamp: new Date().toISOString()\n    });\n\n    // In production, you might want to send this to an analytics service\n    // Example: Google Analytics, Mixpanel, etc.\n    if (window.gtag) {\n      window.gtag('event', 'ai_feedback', {\n        event_category: 'AI Suggestions',\n        event_label: suggestionType,\n        value: feedbackType === 'accept' ? 1 : 0\n      });\n    }\n  }\n\n  /**\n   * Clear feedback cache (useful for testing or user logout)\n   */\n  clearCache() {\n    this.cache.clear();\n  }\n\n  /**\n   * Check if feedback has been submitted for a suggestion\n   * @param {string} suggestionId - Suggestion identifier\n   * @returns {string|null} Feedback type if exists, null otherwise\n   */\n  getCachedFeedback(suggestionId) {\n    for (const [key, value] of this.cache.entries()) {\n      if (key.startsWith(suggestionId + '_')) {\n        return value.feedback_type;\n      }\n    }\n    return null;\n  }\n}\n\n// Create and export a singleton instance\nconst feedbackService = new FeedbackService();\nexport default feedbackService;\n\n// Export the class for testing purposes\nexport { FeedbackService };", "map": {"version": 3, "names": ["FeedbackService", "constructor", "baseUrl", "cache", "Map", "submitFeedback", "suggestionId", "feedbackType", "suggestionType", "metadata", "cache<PERSON>ey", "has", "console", "log", "get", "payload", "suggestion_id", "feedback_type", "suggestion_type", "timestamp", "Date", "toISOString", "user_agent", "navigator", "userAgent", "screen_resolution", "window", "screen", "width", "height", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "status", "statusText", "result", "json", "set", "logFeedbackEvent", "error", "fallback<PERSON><PERSON><PERSON>", "success", "message", "getFeedbackStats", "total_feedback", "accepted", "rejected", "acceptance_rate", "getFeedback", "gtag", "event_category", "event_label", "value", "clearCache", "clear", "getCached<PERSON>eedback", "key", "entries", "startsWith", "feedbackService"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/capstone final project/SafeRoomAI-Athira/frontend/src/services/feedbackService.js"], "sourcesContent": ["// src/services/feedbackService.js\n\n/**\n * Service for handling AI feedback submissions\n */\nclass FeedbackService {\n  constructor() {\n    this.baseUrl = '/api/feedback';\n    this.cache = new Map(); // Cache feedback to prevent duplicate submissions\n  }\n\n  /**\n   * Submit feedback for an AI suggestion\n   * @param {string} suggestionId - Unique identifier for the suggestion\n   * @param {string} feedbackType - 'accept' or 'reject'\n   * @param {string} suggestionType - Type of suggestion (e.g., 'anomaly', 'alert', 'recommendation')\n   * @param {Object} metadata - Additional context about the suggestion\n   * @returns {Promise<Object>} Response from the server\n   */\n  async submitFeedback(suggestionId, feedbackType, suggestionType, metadata = {}) {\n    const cacheKey = `${suggestionId}_${feedbackType}`;\n    \n    // Check if feedback already submitted\n    if (this.cache.has(cacheKey)) {\n      console.log('Feedback already submitted for this suggestion');\n      return this.cache.get(cacheKey);\n    }\n\n    const payload = {\n      suggestion_id: suggestionId,\n      feedback_type: feedbackType,\n      suggestion_type: suggestionType,\n      timestamp: new Date().toISOString(),\n      metadata: {\n        user_agent: navigator.userAgent,\n        screen_resolution: `${window.screen.width}x${window.screen.height}`,\n        ...metadata\n      }\n    };\n\n    try {\n      const response = await fetch(`${this.baseUrl}/submit`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(payload)\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      const result = await response.json();\n      \n      // Cache the successful response\n      this.cache.set(cacheKey, result);\n      \n      // Log for analytics\n      this.logFeedbackEvent(suggestionId, feedbackType, suggestionType);\n      \n      return result;\n    } catch (error) {\n      console.error('Failed to submit feedback:', error);\n      \n      // For now, simulate success to avoid blocking the UI\n      // In production, you might want to queue failed requests for retry\n      const fallbackResult = {\n        success: true,\n        message: 'Feedback recorded locally',\n        suggestion_id: suggestionId,\n        feedback_type: feedbackType\n      };\n      \n      this.cache.set(cacheKey, fallbackResult);\n      return fallbackResult;\n    }\n  }\n\n  /**\n   * Get feedback statistics for analytics\n   * @returns {Promise<Object>} Feedback statistics\n   */\n  async getFeedbackStats() {\n    try {\n      const response = await fetch(`${this.baseUrl}/stats`);\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error('Failed to fetch feedback stats:', error);\n      return {\n        total_feedback: 0,\n        accepted: 0,\n        rejected: 0,\n        acceptance_rate: 0\n      };\n    }\n  }\n\n  /**\n   * Get feedback for a specific suggestion\n   * @param {string} suggestionId - Suggestion identifier\n   * @returns {Promise<Object|null>} Existing feedback or null\n   */\n  async getFeedback(suggestionId) {\n    try {\n      const response = await fetch(`${this.baseUrl}/${suggestionId}`);\n      if (response.status === 404) {\n        return null;\n      }\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}`);\n      }\n      return await response.json();\n    } catch (error) {\n      console.error('Failed to fetch feedback:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Log feedback event for analytics\n   * @private\n   */\n  logFeedbackEvent(suggestionId, feedbackType, suggestionType) {\n    // Log to console for development\n    console.log('Feedback Event:', {\n      suggestion_id: suggestionId,\n      feedback_type: feedbackType,\n      suggestion_type: suggestionType,\n      timestamp: new Date().toISOString()\n    });\n\n    // In production, you might want to send this to an analytics service\n    // Example: Google Analytics, Mixpanel, etc.\n    if (window.gtag) {\n      window.gtag('event', 'ai_feedback', {\n        event_category: 'AI Suggestions',\n        event_label: suggestionType,\n        value: feedbackType === 'accept' ? 1 : 0\n      });\n    }\n  }\n\n  /**\n   * Clear feedback cache (useful for testing or user logout)\n   */\n  clearCache() {\n    this.cache.clear();\n  }\n\n  /**\n   * Check if feedback has been submitted for a suggestion\n   * @param {string} suggestionId - Suggestion identifier\n   * @returns {string|null} Feedback type if exists, null otherwise\n   */\n  getCachedFeedback(suggestionId) {\n    for (const [key, value] of this.cache.entries()) {\n      if (key.startsWith(suggestionId + '_')) {\n        return value.feedback_type;\n      }\n    }\n    return null;\n  }\n}\n\n// Create and export a singleton instance\nconst feedbackService = new FeedbackService();\nexport default feedbackService;\n\n// Export the class for testing purposes\nexport { FeedbackService };\n"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA,MAAMA,eAAe,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,eAAe;IAC9B,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,cAAcA,CAACC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAE;IAC9E,MAAMC,QAAQ,GAAG,GAAGJ,YAAY,IAAIC,YAAY,EAAE;;IAElD;IACA,IAAI,IAAI,CAACJ,KAAK,CAACQ,GAAG,CAACD,QAAQ,CAAC,EAAE;MAC5BE,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7D,OAAO,IAAI,CAACV,KAAK,CAACW,GAAG,CAACJ,QAAQ,CAAC;IACjC;IAEA,MAAMK,OAAO,GAAG;MACdC,aAAa,EAAEV,YAAY;MAC3BW,aAAa,EAAEV,YAAY;MAC3BW,eAAe,EAAEV,cAAc;MAC/BW,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCZ,QAAQ,EAAE;QACRa,UAAU,EAAEC,SAAS,CAACC,SAAS;QAC/BC,iBAAiB,EAAE,GAAGC,MAAM,CAACC,MAAM,CAACC,KAAK,IAAIF,MAAM,CAACC,MAAM,CAACE,MAAM,EAAE;QACnE,GAAGpB;MACL;IACF,CAAC;IAED,IAAI;MACF,MAAMqB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAAC7B,OAAO,SAAS,EAAE;QACrD8B,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACrB,OAAO;MAC9B,CAAC,CAAC;MAEF,IAAI,CAACe,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,QAAQR,QAAQ,CAACS,MAAM,KAAKT,QAAQ,CAACU,UAAU,EAAE,CAAC;MACpE;MAEA,MAAMC,MAAM,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC;;MAEpC;MACA,IAAI,CAACvC,KAAK,CAACwC,GAAG,CAACjC,QAAQ,EAAE+B,MAAM,CAAC;;MAEhC;MACA,IAAI,CAACG,gBAAgB,CAACtC,YAAY,EAAEC,YAAY,EAAEC,cAAc,CAAC;MAEjE,OAAOiC,MAAM;IACf,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;MAElD;MACA;MACA,MAAMC,cAAc,GAAG;QACrBC,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE,2BAA2B;QACpChC,aAAa,EAAEV,YAAY;QAC3BW,aAAa,EAAEV;MACjB,CAAC;MAED,IAAI,CAACJ,KAAK,CAACwC,GAAG,CAACjC,QAAQ,EAAEoC,cAAc,CAAC;MACxC,OAAOA,cAAc;IACvB;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMG,gBAAgBA,CAAA,EAAG;IACvB,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAAC7B,OAAO,QAAQ,CAAC;MACrD,IAAI,CAAC4B,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,QAAQR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAC5C;MACA,OAAO,MAAMT,QAAQ,CAACY,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO;QACLK,cAAc,EAAE,CAAC;QACjBC,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,CAAC;QACXC,eAAe,EAAE;MACnB,CAAC;IACH;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMC,WAAWA,CAAChD,YAAY,EAAE;IAC9B,IAAI;MACF,MAAMwB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAAC7B,OAAO,IAAII,YAAY,EAAE,CAAC;MAC/D,IAAIwB,QAAQ,CAACS,MAAM,KAAK,GAAG,EAAE;QAC3B,OAAO,IAAI;MACb;MACA,IAAI,CAACT,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,QAAQR,QAAQ,CAACS,MAAM,EAAE,CAAC;MAC5C;MACA,OAAO,MAAMT,QAAQ,CAACY,IAAI,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdjC,OAAO,CAACiC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,IAAI;IACb;EACF;;EAEA;AACF;AACA;AACA;EACED,gBAAgBA,CAACtC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAE;IAC3D;IACAI,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;MAC7BG,aAAa,EAAEV,YAAY;MAC3BW,aAAa,EAAEV,YAAY;MAC3BW,eAAe,EAAEV,cAAc;MAC/BW,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC,CAAC;;IAEF;IACA;IACA,IAAIK,MAAM,CAAC6B,IAAI,EAAE;MACf7B,MAAM,CAAC6B,IAAI,CAAC,OAAO,EAAE,aAAa,EAAE;QAClCC,cAAc,EAAE,gBAAgB;QAChCC,WAAW,EAAEjD,cAAc;QAC3BkD,KAAK,EAAEnD,YAAY,KAAK,QAAQ,GAAG,CAAC,GAAG;MACzC,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;EACEoD,UAAUA,CAAA,EAAG;IACX,IAAI,CAACxD,KAAK,CAACyD,KAAK,CAAC,CAAC;EACpB;;EAEA;AACF;AACA;AACA;AACA;EACEC,iBAAiBA,CAACvD,YAAY,EAAE;IAC9B,KAAK,MAAM,CAACwD,GAAG,EAAEJ,KAAK,CAAC,IAAI,IAAI,CAACvD,KAAK,CAAC4D,OAAO,CAAC,CAAC,EAAE;MAC/C,IAAID,GAAG,CAACE,UAAU,CAAC1D,YAAY,GAAG,GAAG,CAAC,EAAE;QACtC,OAAOoD,KAAK,CAACzC,aAAa;MAC5B;IACF;IACA,OAAO,IAAI;EACb;AACF;;AAEA;AACA,MAAMgD,eAAe,GAAG,IAAIjE,eAAe,CAAC,CAAC;AAC7C,eAAeiE,eAAe;;AAE9B;AACA,SAASjE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}