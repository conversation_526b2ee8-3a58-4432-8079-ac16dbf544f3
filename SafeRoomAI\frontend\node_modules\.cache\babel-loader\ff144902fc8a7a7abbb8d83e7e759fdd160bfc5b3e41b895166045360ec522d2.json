{"ast": null, "code": "export { default, createFilterOptions } from \"./Autocomplete.js\";\nexport { default as autocompleteClasses } from \"./autocompleteClasses.js\";\nexport * from \"./autocompleteClasses.js\";", "map": {"version": 3, "names": ["default", "createFilterOptions", "autocompleteClasses"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/capstone final project/SafeRoomAI/frontend/node_modules/@mui/material/Autocomplete/index.js"], "sourcesContent": ["export { default, createFilterOptions } from \"./Autocomplete.js\";\nexport { default as autocompleteClasses } from \"./autocompleteClasses.js\";\nexport * from \"./autocompleteClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,EAAEC,mBAAmB,QAAQ,mBAAmB;AAChE,SAASD,OAAO,IAAIE,mBAAmB,QAAQ,0BAA0B;AACzE,cAAc,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}