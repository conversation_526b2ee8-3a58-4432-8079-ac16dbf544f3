import e from"postcss-value-parser";var l=new Map([["block,flow","block"],["block,flow-root","flow-root"],["inline,flow","inline"],["inline,flow-root","inline-block"],["run-in,flow","run-in"],["list-item,block,flow","list-item"],["inline,flow,list-item","inline list-item"],["block,flex","flex"],["inline,flex","inline-flex"],["block,grid","grid"],["inline,grid","inline-grid"],["inline,ruby","ruby"],["block,table","table"],["inline,table","inline-table"],["table-cell,flow","table-cell"],["table-caption,flow","table-caption"],["ruby-base,flow","ruby-base"],["ruby-text,flow","ruby-text"]]);const n=n=>{const o=!("preserve"in Object(n))||Boolean(n.preserve);return{postcssPlugin:"postcss-normalize-display-values",prepare(){const n=new Map;return{Declaration(t){if("display"!==t.prop.toLowerCase())return;const i=t.value;if(!i)return;if(n.has(i))return void(t.value!==n.get(i)&&(t.cloneBefore({value:n.get(i)}),o||t.remove()));const r=function(n){const{nodes:o}=e(n);if(1===o.length)return n;const t=o.filter((e=>"word"===e.type)).map((e=>e.value.toLowerCase()));if(t.length<=1)return n;return l.get(t.join(","))||n}(i);n.set(i,r),t.value!==r&&(t.cloneBefore({value:r}),o||t.remove())}}}}};n.postcss=!0;export{n as default};
