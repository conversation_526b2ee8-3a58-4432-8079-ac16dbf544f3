
/* src/App.css */
:root {
  --bg-color: #ffffff;
  --text-color: #333333;
  --accent-color: #007bff;
}

body.dark-mode {
  --bg-color: #121212;
  --text-color: #ffffff;
  --accent-color: #0d6efd;
}

body {
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s, color 0.3s;
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

header {
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

main {
  flex: 1;
  padding: 2rem;
}

.toggle-btn {
  background-color: var(--accent-color);
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
  font-weight: bold;
  color: #fff;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: opacity 0.3s;
}

.toggle-btn:hover {
  opacity: 0.8;
}

.home-screen {
  text-align: center;
}

.video-container {
  margin-top: 1rem;
}

.video-container img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.notfound-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 5rem;
  color: var(--text-color);
}

.notfound-icon {
  font-size: 5rem;
  color: var(--accent-color);
  margin-bottom: 1rem;
}

.btn-back {
  display: inline-block;
  margin-top: 1rem;
  background-color: var(--accent-color);
  color: #fff;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  text-decoration: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn-back:hover {
  opacity: 0.8;
}

.footer {
  text-align: center;
  padding: 1rem;
  border-top: 1px solid rgba(0,0,0,0.1);
}

body.dark-mode .footer {
  border-color: rgba(255,255,255,0.2);
}

.nav-bar {
  display: flex;
  gap: 1rem;
  padding: 0.5rem 2rem;
  background-color: var(--bg-color);
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

.nav-link {
  text-decoration: none;
  font-weight: 500;
  color: var(--text-color);
  transition: color 0.2s;
}

.nav-link:hover {
  color: var(--accent-color);
}

body.dark-mode .nav-bar {
  background-color: var(--bg-color);
  border-color: rgba(255,255,255,0.2);
}
body.dark-mode .nav-link:hover {
  color: var(--accent-color);
}


.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto‐fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.activity-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.activity-image {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.activity-timestamp {
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-color);
}

body.dark-mode .activity-timestamp {
  color: var(--text-color);
}

.analytics-screen h3 {
  text-align: center;
}

.chart-container {
  margin: 2rem 0;
}

.chart-container h3 {
  margin-bottom: 1rem;
}

/* ──────────────────────────────────────────────────────────────────────────── */
/* Activity Feed Enhancements */
.activity-feed {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.activity-feed h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  border-bottom: 2px solid var(--accent-color);
  display: inline-block;
  padding-bottom: 0.25rem;
}

.error-text {
  color: #cc0000;
  font-weight: 500;
}

/* grid container: center, gap, responsive */
.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
  justify-items: center;
}

/* each “card” */
.activity-item {
  background-color: var(--bg-color);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  width: 100%;
  max-width: 220px;
}

.activity-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.activity-image {
  width: 100%;
  height: 140px;
  object-fit: cover; /* crops if necessary, preserves aspect ratio */
  display: block;
}

.activity-timestamp {
  padding: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-color);
  background-color: var(--bg-color);
  text-align: center;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* If dark mode, slightly adjust border color */
body.dark-mode .activity-item {
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 6px rgba(255, 255, 255, 0.05);
}

body.dark-mode .activity-item:hover {
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.08);
}

body.dark-mode .activity-timestamp {
  background-color: var(--bg-color);
  color: var(--text-color);
}

/* ──────────────────────────────────────────────────────────────────────────── */
/* ──────────────────────────────────────────────────────────────────────────── */
/* Reusable “card” style for charts/videos */
.card {
  background-color: var(--bg-color);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 2rem;
  transition: box-shadow 0.2s;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Title styles */
.analytics-screen h2,
.home-screen h2 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-color);
  border-bottom: 2px solid var(--accent-color);
  display: inline-block;
  padding-bottom: 0.25rem;
}

/* Error message */
.error-text {
  color: #cc0000;
  font-weight: 500;
  margin-bottom: 1rem;
}

/* Chart container (already wrapped in .card) */
.chart-container {
  margin-top: 1rem;
}

/* Center and constrain width */
.analytics-screen,
.home-screen {
  max-width: 1000px;
  margin: 0 auto;
  padding: 1rem;
}

/* Style for the live-stream image */
.video-card {
  width: 100%;
  max-width: 800px;
  margin: 1rem auto;
}

.video-card img {
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  display: block;
}

/* Adjust dark-mode shadows/borders */
body.dark-mode .card {
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 6px rgba(255, 255, 255, 0.05);
}

body.dark-mode .card:hover {
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.08);
}

/* ──────────────────────────────────────────────────────────────────────────── */

/* Real-time Chart Styles */
.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-color);
  font-style: italic;
}

.chart-error {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 12px;
  border-left: 3px solid #ef4444;
}

/* Anomaly Chart Styles */
.anomaly-chart-container {
  height: 100%;
  position: relative;
}

/* Heatmap Styles */
.anomaly-heatmap-container {
  padding: 10px;
}

.heatmap-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  margin-bottom: 15px;
}

.heatmap-cell {
  border: 1px solid rgba(0,0,0,0.1);
}

.heatmap-cell-empty {
  opacity: 0.3;
}

.heatmap-cell-low {
  opacity: 0.6;
}

.heatmap-cell-medium {
  opacity: 0.8;
}

.heatmap-cell-high {
  opacity: 1;
}

.heatmap-legend {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 12px;
  color: var(--text-color);
}

.legend-scale {
  display: flex;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid rgba(0,0,0,0.2);
}

/* Snapshots Styles - Stable Layout */
.anomaly-snapshots-container {
  padding: 20px 10px;
  min-height: 200px;
  max-height: 300px;
  position: relative;
  overflow: hidden;
  contain: layout style paint;
}

.snapshots-grid {
  display: flex;
  gap: 15px;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 20px;
  min-height: 180px;
  max-height: 260px;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  position: relative;
}

.snapshots-grid::-webkit-scrollbar {
  height: 6px;
}

.snapshots-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.snapshots-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.snapshots-grid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.snapshot-item {
  min-width: 150px;
  max-width: 150px;
  text-align: center;
  position: relative;
  flex-shrink: 0;
  margin-bottom: 10px;
  will-change: transform;
  transform: translateZ(0);
}

.snapshot-image-container {
  position: relative;
  display: inline-block;
  width: 150px;
  height: 100px;
  overflow: hidden;
}

.snapshot-image {
  transition: transform 0.2s ease;
  width: 150px !important;
  height: 100px !important;
  object-fit: cover;
  display: block;
}

.snapshot-image:hover {
  transform: scale(1.05);
}

.no-snapshots {
  text-align: center;
  color: var(--text-color);
  opacity: 0.7;
  padding: 40px;
  font-style: italic;
}

/* AI Feedback Buttons Styles */
.ai-feedback-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
  background-color: var(--bg-color);
  border: 1px solid rgba(0, 0, 0, 0.12);
  transition: all 0.2s ease;
}

.ai-feedback-container:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ai-feedback-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 6px;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.ai-feedback-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ai-feedback-button.accept {
  color: #10b981;
  border-color: #10b981;
}

.ai-feedback-button.accept:hover {
  background-color: #10b981;
  color: white;
}

.ai-feedback-button.reject {
  color: #ef4444;
  border-color: #ef4444;
}

.ai-feedback-button.reject:hover {
  background-color: #ef4444;
  color: white;
}

.ai-feedback-button.selected {
  font-weight: bold;
}

.ai-feedback-button.selected.accept {
  background-color: #10b981;
  color: white;
}

.ai-feedback-button.selected.reject {
  background-color: #ef4444;
  color: white;
}

.ai-feedback-confirmation {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.ai-feedback-confirmation.success {
  background-color: #dcfce7;
  color: #166534;
}

.ai-feedback-confirmation.info {
  background-color: #dbeafe;
  color: #1e40af;
}

/* Enhanced GitHub-style feedback animations */
@keyframes feedbackPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.ai-feedback-button:active {
  animation: feedbackPulse 0.2s ease-in-out;
}

/* Feedback status indicators */
.feedback-accepted {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2) !important;
}

.feedback-rejected {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
}

/* Smooth transitions for all feedback elements */
.snapshot-item,
.analytics-card,
.activity-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover effects for better UX */
.ai-feedback-container:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Prevent page jumping and ensure stable layout */
.analytics-container {
  scroll-behavior: smooth;
  position: relative;
}

.analytics-section {
  position: relative;
  contain: layout style paint;
}

/* Prevent unwanted scrolling on feedback interactions */
.ai-feedback-buttons button {
  outline: none;
  border: none;
  background: none;
  cursor: pointer;
}

.ai-feedback-buttons button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Prevent page jumping on button clicks */
.ai-feedback-buttons button:focus,
.ai-feedback-buttons button:active {
  outline: none !important;
  box-shadow: none !important;
}

/* Prevent scroll on focus for Material-UI buttons */
.MuiButton-root:focus,
.MuiIconButton-root:focus {
  outline: none !important;
  scroll-behavior: auto !important;
}

/* Ensure stable positioning for feedback containers */
.ai-feedback-container {
  position: relative;
  scroll-margin: 0;
  scroll-padding: 0;
}

/* Stable snapshot layout */
.snapshot-feedback-container {
  position: relative;
  z-index: 1;
  background: white;
  border-radius: 4px;
  padding: 4px;
  margin-top: 8px;
}

/* Global prevention of unwanted scrolling */
html {
  scroll-behavior: auto;
}

/* Disable smooth scrolling on analytics page specifically */
.analytics-container,
.analytics-container * {
  scroll-behavior: auto !important;
}

/* Prevent focus-induced scrolling */
*:focus {
  scroll-margin: 0 !important;
  scroll-padding: 0 !important;
}

/* Specific fixes for button interactions */
button:focus,
button:active,
.MuiButton-root:focus,
.MuiButton-root:active,
.MuiIconButton-root:focus,
.MuiIconButton-root:active {
  outline: none !important;
  scroll-behavior: auto !important;
  transform: none !important;
}

/* Aggressive scroll prevention for analytics page */
.analytics-container {
  position: relative;
  overflow-anchor: none;
}

.analytics-container * {
  scroll-behavior: auto !important;
  overflow-anchor: none;
}

/* Prevent any element from causing scroll jumps */
.analytics-container button,
.analytics-container .MuiButton-root,
.analytics-container .MuiIconButton-root {
  position: relative;
  overflow-anchor: none;
  scroll-margin: 0 !important;
  scroll-padding: 0 !important;
}

/* Force stable positioning for snapshot section */
.snapshot-item,
.snapshot-image-container,
.ai-feedback-container {
  position: relative;
  overflow-anchor: none;
  contain: layout style;
}

/* Prevent layout shifts in analytics sections */
.analytics-section * {
  box-sizing: border-box;
}

.analytics-section button {
  will-change: auto;
  backface-visibility: hidden;
  transform: translateZ(0);
}
