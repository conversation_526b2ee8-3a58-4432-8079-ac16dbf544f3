[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\theme\\theme.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\screens\\Analytics.jsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\NavBar.jsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\screens\\ActivityFeed.jsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\Footer.jsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\screens\\HomeScreen.jsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\NotFound.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\LoadingSpinner.jsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\ErrorAlert.jsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\anomalyheatmap.jsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\anomalychart.jsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\anomalysnapshots.jsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\StatsCard.jsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\DarkModeToggle.jsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\AIFeedbackButtons.jsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\hooks\\useFeedbackState.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\services\\feedbackService.js": "19"}, {"size": 263, "mtime": 1750272660730, "results": "20", "hashOfConfig": "21"}, {"size": 1722, "mtime": 1750989986392, "results": "22", "hashOfConfig": "21"}, {"size": 3774, "mtime": 1750272641708, "results": "23", "hashOfConfig": "21"}, {"size": 16815, "mtime": 1752707061078, "results": "24", "hashOfConfig": "21"}, {"size": 2357, "mtime": 1750272641697, "results": "25", "hashOfConfig": "21"}, {"size": 11035, "mtime": 1752705916038, "results": "26", "hashOfConfig": "21"}, {"size": 558, "mtime": 1750272641694, "results": "27", "hashOfConfig": "21"}, {"size": 5196, "mtime": 1750272641705, "results": "28", "hashOfConfig": "21"}, {"size": 1289, "mtime": 1750272641697, "results": "29", "hashOfConfig": "21"}, {"size": 556, "mtime": 1750272641695, "results": "30", "hashOfConfig": "21"}, {"size": 885, "mtime": 1750272641692, "results": "31", "hashOfConfig": "21"}, {"size": 3972, "mtime": 1750272660726, "results": "32", "hashOfConfig": "21"}, {"size": 2980, "mtime": 1750272660724, "results": "33", "hashOfConfig": "21"}, {"size": 7663, "mtime": 1752707106484, "results": "34", "hashOfConfig": "21"}, {"size": 1958, "mtime": 1750272641698, "results": "35", "hashOfConfig": "21"}, {"size": 709, "mtime": 1750272641692, "results": "36", "hashOfConfig": "21"}, {"size": 8032, "mtime": 1752706467913, "results": "37", "hashOfConfig": "21"}, {"size": 3965, "mtime": 1752697816224, "results": "38", "hashOfConfig": "21"}, {"size": 5010, "mtime": 1752697541051, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1dm12nb", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\theme\\theme.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\screens\\Analytics.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\NavBar.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\screens\\ActivityFeed.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\Footer.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\screens\\HomeScreen.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\NotFound.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\LoadingSpinner.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\ErrorAlert.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\anomalyheatmap.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\anomalychart.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\anomalysnapshots.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\StatsCard.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\DarkModeToggle.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\components\\AIFeedbackButtons.jsx", ["97"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\hooks\\useFeedbackState.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\capstone final project\\SafeRoomAI-Athira\\frontend\\src\\services\\feedbackService.js", [], [], {"ruleId": "98", "severity": 1, "message": "99", "line": 6, "column": 3, "nodeType": "100", "messageId": "101", "endLine": 6, "endColumn": 10}, "no-unused-vars", "'Tooltip' is defined but never used.", "Identifier", "unusedVar"]