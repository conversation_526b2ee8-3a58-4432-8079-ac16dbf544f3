// src/components/AIFeedbackButtons.jsx
import React, { useState } from 'react';
import {
  Box,
  IconButton,
  Tooltip,
  Chip,
  Typography,
  Fade,
  CircularProgress
} from '@mui/material';
import {
  ThumbUp,
  ThumbDown,
  Check,
  Close,
  Feedback
} from '@mui/icons-material';

const AIFeedbackButtons = ({ 
  suggestionId, 
  suggestionType = 'anomaly',
  onFeedback,
  size = 'small',
  variant = 'default', // 'default', 'compact', 'inline'
  disabled = false,
  initialFeedback = null
}) => {
  const [feedback, setFeedback] = useState(initialFeedback);
  const [loading, setLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleFeedback = async (type) => {
    if (disabled || loading) return;
    
    setLoading(true);
    try {
      // Call the parent callback
      if (onFeedback) {
        await onFeedback(suggestionId, type, suggestionType);
      }
      
      setFeedback(type);
      setShowConfirmation(true);
      
      // Hide confirmation after 2 seconds
      setTimeout(() => {
        setShowConfirmation(false);
      }, 2000);
      
    } catch (error) {
      console.error('Failed to submit feedback:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFeedbackColor = (type) => {
    if (feedback === type) {
      return type === 'accept' ? 'success' : 'error';
    }
    return 'default';
  };

  const getFeedbackIcon = (type) => {
    if (loading && feedback === type) {
      return <CircularProgress size={16} />;
    }
    return type === 'accept' ? <ThumbUp /> : <ThumbDown />;
  };

  // Compact variant for tight spaces
  if (variant === 'compact') {
    return (
      <Box display="flex" alignItems="center" gap={0.5}>
        <Tooltip title={feedback === 'accept' ? 'Marked as helpful' : 'Mark as helpful'}>
          <IconButton
            size="small"
            onClick={() => handleFeedback('accept')}
            disabled={disabled || loading}
            color={getFeedbackColor('accept')}
            sx={{ 
              p: 0.5,
              '&:hover': { backgroundColor: 'success.light', color: 'success.contrastText' }
            }}
          >
            {getFeedbackIcon('accept')}
          </IconButton>
        </Tooltip>
        
        <Tooltip title={feedback === 'reject' ? 'Marked as not helpful' : 'Mark as not helpful'}>
          <IconButton
            size="small"
            onClick={() => handleFeedback('reject')}
            disabled={disabled || loading}
            color={getFeedbackColor('reject')}
            sx={{ 
              p: 0.5,
              '&:hover': { backgroundColor: 'error.light', color: 'error.contrastText' }
            }}
          >
            {getFeedbackIcon('reject')}
          </IconButton>
        </Tooltip>
      </Box>
    );
  }

  // Inline variant for integration within text/content
  if (variant === 'inline') {
    return (
      <Box display="inline-flex" alignItems="center" gap={1} ml={1}>
        <Typography variant="caption" color="text.secondary">
          Helpful?
        </Typography>
        <Box display="flex" gap={0.5}>
          <IconButton
            size="small"
            onClick={() => handleFeedback('accept')}
            disabled={disabled || loading}
            color={getFeedbackColor('accept')}
            sx={{ p: 0.25 }}
          >
            {getFeedbackIcon('accept')}
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleFeedback('reject')}
            disabled={disabled || loading}
            color={getFeedbackColor('reject')}
            sx={{ p: 0.25 }}
          >
            {getFeedbackIcon('reject')}
          </IconButton>
        </Box>
      </Box>
    );
  }

  // Default variant - similar to GitHub's suggestion interface
  return (
    <Box 
      display="flex" 
      alignItems="center" 
      gap={1}
      sx={{
        p: 1,
        borderRadius: 1,
        backgroundColor: 'background.paper',
        border: '1px solid',
        borderColor: 'divider',
        transition: 'all 0.2s ease'
      }}
    >
      <Feedback fontSize="small" color="action" />
      <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
        Was this AI suggestion helpful?
      </Typography>
      
      <Fade in={showConfirmation}>
        <Chip
          icon={feedback === 'accept' ? <Check /> : <Close />}
          label={feedback === 'accept' ? 'Thanks!' : 'Thanks for the feedback'}
          size="small"
          color={feedback === 'accept' ? 'success' : 'default'}
          sx={{ mr: 1 }}
        />
      </Fade>
      
      {!showConfirmation && (
        <Box display="flex" gap={0.5}>
          <Tooltip title="Yes, this was helpful">
            <IconButton
              size={size}
              onClick={() => handleFeedback('accept')}
              disabled={disabled || loading}
              color={getFeedbackColor('accept')}
              sx={{
                border: '1px solid',
                borderColor: feedback === 'accept' ? 'success.main' : 'divider',
                '&:hover': { 
                  backgroundColor: 'success.light', 
                  borderColor: 'success.main',
                  color: 'success.contrastText'
                }
              }}
            >
              {getFeedbackIcon('accept')}
            </IconButton>
          </Tooltip>
          
          <Tooltip title="No, this was not helpful">
            <IconButton
              size={size}
              onClick={() => handleFeedback('reject')}
              disabled={disabled || loading}
              color={getFeedbackColor('reject')}
              sx={{
                border: '1px solid',
                borderColor: feedback === 'reject' ? 'error.main' : 'divider',
                '&:hover': { 
                  backgroundColor: 'error.light', 
                  borderColor: 'error.main',
                  color: 'error.contrastText'
                }
              }}
            >
              {getFeedbackIcon('reject')}
            </IconButton>
          </Tooltip>
        </Box>
      )}
    </Box>
  );
};

export default AIFeedbackButtons;
