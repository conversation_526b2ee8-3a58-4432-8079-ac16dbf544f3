{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\capstone final project\\\\SafeRoomAI-Athira\\\\frontend\\\\src\\\\screens\\\\Analytics.jsx\",\n  _s = $RefreshSig$();\n// src/screens/Analytics.jsx\n\nimport React, { useState, useEffect } from 'react';\nimport { Container, Typography, Grid, Card, CardContent, Box, IconButton, FormControl, InputLabel, Select, MenuItem, Chip } from '@mui/material';\nimport { Refresh, TrendingUp, Warning, Assessment, Timeline } from '@mui/icons-material';\nimport { LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';\nimport '../App.css';\nimport { format } from 'date-fns';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport ErrorAlert from '../components/ErrorAlert';\nimport StatsCard from '../components/StatsCard';\nimport AnomalyChart from '../components/anomalychart';\nimport AnomalyHeatmap from '../components/anomalyheatmap';\nimport AnomalySnapshots from '../components/anomalysnapshots';\nimport AIFeedbackButtons from '../components/AIFeedbackButtons';\nimport useFeedbackState from '../hooks/useFeedbackState';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Analytics() {\n  _s();\n  const [summary, setSummary] = useState([]);\n  const [errors, setErrors] = useState([]);\n  const [errorMsg, setErrorMsg] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [timeRange, setTimeRange] = useState('24h');\n\n  // Use the feedback state hook\n  const {\n    feedbackStates,\n    submitFeedback\n  } = useFeedbackState('analytics');\n  useEffect(() => {\n    fetchData();\n  }, []);\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      // Fetch anomalies‐per‐minute summary\n      const res1 = await fetch('/predict/analytics/summary');\n      if (!res1.ok) throw new Error(`Summary HTTP ${res1.status}`);\n      const summaryJson = await res1.json();\n      if (summaryJson && typeof summaryJson === 'object' && !Array.isArray(summaryJson)) {\n        const summaryArr = Object.entries(summaryJson).map(([time, count]) => ({\n          time,\n          count,\n          formattedTime: format(new Date(time), 'HH:mm')\n        }));\n        setSummary(summaryArr);\n      } else {\n        setSummary([]);\n      }\n\n      // Fetch recent reconstruction errors\n      const res2 = await fetch('/predict/analytics/errors');\n      if (!res2.ok) throw new Error(`Errors HTTP ${res2.status}`);\n      const errorsJson = await res2.json();\n      if (Array.isArray(errorsJson)) {\n        setErrors(errorsJson);\n      } else {\n        setErrors([]);\n      }\n      setErrorMsg(null);\n    } catch (e) {\n      console.error('Analytics fetch error:', e);\n      setErrorMsg('Failed to load analytics data.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Calculate statistics\n  const totalAnomalies = summary.reduce((sum, item) => sum + item.count, 0);\n  const avgErrorRate = errors.length > 0 ? (errors.reduce((sum, err) => sum + err, 0) / errors.length).toFixed(4) : 0;\n  const maxAnomaliesPerMinute = summary.length > 0 ? Math.max(...summary.map(item => item.count)) : 0;\n\n  // Prepare error distribution data\n  const errorDistribution = errors.map((error, index) => ({\n    index: index + 1,\n    value: parseFloat(error.toFixed(4)),\n    category: error > 0.5 ? 'High' : error > 0.2 ? 'Medium' : 'Low'\n  }));\n  const handleFeedback = async (suggestionId, feedbackType, suggestionType) => {\n    await submitFeedback(suggestionId, feedbackType, suggestionType, {\n      analytics_data: {\n        total_anomalies: totalAnomalies,\n        avg_error_rate: avgErrorRate,\n        time_range: timeRange\n      }\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      message: \"Loading analytics data...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        fontWeight: \"bold\",\n        children: \"Analytics Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"Real-time insights and anomaly detection metrics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      gap: 2,\n      mb: 3,\n      flexWrap: \"wrap\",\n      alignItems: \"center\",\n      children: [/*#__PURE__*/_jsxDEV(FormControl, {\n        size: \"small\",\n        sx: {\n          minWidth: 120\n        },\n        children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n          children: \"Time Range\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          value: timeRange,\n          label: \"Time Range\",\n          onChange: e => setTimeRange(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"1h\",\n            children: \"Last Hour\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"24h\",\n            children: \"Last 24 Hours\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"7d\",\n            children: \"Last 7 Days\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: fetchData,\n        disabled: loading,\n        children: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        icon: /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 17\n        }, this),\n        label: `${summary.length} data points`,\n        color: \"primary\",\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), errorMsg && /*#__PURE__*/_jsxDEV(ErrorAlert, {\n      message: errorMsg,\n      onRetry: fetchData,\n      sx: {\n        mb: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"Total Anomalies\",\n          value: totalAnomalies,\n          subtitle: \"Detected events\",\n          icon: /*#__PURE__*/_jsxDEV(Warning, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 19\n          }, this),\n          color: \"warning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"Avg Error Rate\",\n          value: avgErrorRate,\n          subtitle: \"Reconstruction error\",\n          icon: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 19\n          }, this),\n          color: \"error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"Peak Activity\",\n          value: maxAnomaliesPerMinute,\n          subtitle: \"Max per minute\",\n          icon: /*#__PURE__*/_jsxDEV(Timeline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 19\n          }, this),\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(StatsCard, {\n          title: \"Data Points\",\n          value: errors.length,\n          subtitle: \"Error samples\",\n          icon: /*#__PURE__*/_jsxDEV(Assessment, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 19\n          }, this),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          p: 2,\n          backgroundColor: 'background.default',\n          border: feedbackStates['analytics_summary'] === 'accept' ? '2px solid #10b981' : feedbackStates['analytics_summary'] === 'reject' ? '2px solid #ef4444' : '1px solid',\n          borderColor: feedbackStates['analytics_summary'] ? 'transparent' : 'divider',\n          transition: 'all 0.3s ease'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"space-between\",\n          flexWrap: \"wrap\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 1,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"AI Analytics Insights\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), feedbackStates['analytics_summary'] && /*#__PURE__*/_jsxDEV(Chip, {\n                size: \"small\",\n                label: feedbackStates['analytics_summary'] === 'accept' ? 'Helpful' : 'Needs improvement',\n                color: feedbackStates['analytics_summary'] === 'accept' ? 'success' : 'warning',\n                sx: {\n                  fontSize: '0.7rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"These metrics are generated by our AI anomaly detection system\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              minWidth: '300px'\n            },\n            children: /*#__PURE__*/_jsxDEV(AIFeedbackButtons, {\n              suggestionId: \"analytics_summary\",\n              suggestionType: \"analytics_insights\",\n              onFeedback: handleFeedback,\n              variant: \"default\",\n              initialFeedback: feedbackStates['analytics_summary']\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Anomalies Timeline\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              mb: 2,\n              children: \"Anomaly detections over time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: 350,\n              children: /*#__PURE__*/_jsxDEV(LineChart, {\n                data: summary,\n                children: [/*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"formattedTime\",\n                  tick: {\n                    fontSize: 12\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                  tick: {\n                    fontSize: 12\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  labelFormatter: (label, payload) => {\n                    if (payload && payload[0]) {\n                      return format(new Date(payload[0].payload.time), 'MMM dd, yyyy HH:mm');\n                    }\n                    return label;\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Line, {\n                  type: \"monotone\",\n                  dataKey: \"count\",\n                  stroke: \"#1976d2\",\n                  strokeWidth: 2,\n                  dot: {\n                    fill: '#1976d2',\n                    strokeWidth: 2,\n                    r: 4\n                  },\n                  activeDot: {\n                    r: 6\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Error Distribution\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              mb: 2,\n              children: \"Reconstruction error levels\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: 350,\n              children: /*#__PURE__*/_jsxDEV(BarChart, {\n                data: errorDistribution,\n                children: [/*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"index\",\n                  tick: {\n                    fontSize: 10\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                  tick: {\n                    fontSize: 10\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  formatter: value => [value.toFixed(4), 'Error Rate']\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                  dataKey: \"value\",\n                  fill: \"#1976d2\",\n                  radius: [2, 2, 0, 0]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                children: \"Security Anomaly Chart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1,\n                  p: 1,\n                  backgroundColor: 'background.paper',\n                  borderRadius: 1,\n                  border: '1px solid',\n                  borderColor: 'divider'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  fontWeight: 500,\n                  children: \"Was this chart helpful?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(AIFeedbackButtons, {\n                  suggestionId: \"anomaly_chart\",\n                  suggestionType: \"anomaly_visualization\",\n                  onFeedback: handleFeedback,\n                  variant: \"compact\",\n                  initialFeedback: feedbackStates['anomaly_chart']\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(AnomalyChart, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                children: \"Activity Heatmap\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(AIFeedbackButtons, {\n                suggestionId: \"activity_heatmap\",\n                suggestionType: \"heatmap_visualization\",\n                onFeedback: handleFeedback,\n                variant: \"compact\",\n                initialFeedback: feedbackStates['activity_heatmap']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(AnomalyHeatmap, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Snapshot of Anomalies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(AnomalySnapshots, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n}\n_s(Analytics, \"as9S4lP1pyUPcnBzTFOhEF587Ro=\", false, function () {\n  return [useFeedbackState];\n});\n_c = Analytics;\nvar _c;\n$RefreshReg$(_c, \"Analytics\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Box", "IconButton", "FormControl", "InputLabel", "Select", "MenuItem", "Chip", "Refresh", "TrendingUp", "Warning", "Assessment", "Timeline", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Bar", "format", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StatsCard", "Anoma<PERSON><PERSON><PERSON>", "AnomalyHeatmap", "AnomalySnapshots", "AIFeedbackButtons", "useFeedbackState", "jsxDEV", "_jsxDEV", "Analytics", "_s", "summary", "set<PERSON>ummary", "errors", "setErrors", "errorMsg", "setErrorMsg", "loading", "setLoading", "timeRange", "setTimeRange", "feedbackStates", "submitFeedback", "fetchData", "res1", "fetch", "ok", "Error", "status", "summaryJson", "json", "Array", "isArray", "summaryArr", "Object", "entries", "map", "time", "count", "formattedTime", "Date", "res2", "<PERSON><PERSON><PERSON>", "e", "console", "error", "totalAnomalies", "reduce", "sum", "item", "avgErrorRate", "length", "err", "toFixed", "maxAnomaliesPerMinute", "Math", "max", "errorDistribution", "index", "value", "parseFloat", "category", "handleFeedback", "suggestionId", "feedbackType", "suggestionType", "analytics_data", "total_anomalies", "avg_error_rate", "time_range", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "children", "mb", "variant", "component", "gutterBottom", "fontWeight", "color", "display", "gap", "flexWrap", "alignItems", "size", "sx", "min<PERSON><PERSON><PERSON>", "label", "onChange", "target", "onClick", "disabled", "icon", "onRetry", "container", "spacing", "xs", "sm", "md", "title", "subtitle", "p", "backgroundColor", "border", "borderColor", "transition", "justifyContent", "fontSize", "onFeedback", "initialFeedback", "lg", "width", "height", "data", "dataKey", "tick", "labelFormatter", "payload", "type", "stroke", "strokeWidth", "dot", "fill", "r", "activeDot", "formatter", "radius", "borderRadius", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/capstone final project/SafeRoomAI-Athira/frontend/src/screens/Analytics.jsx"], "sourcesContent": ["// src/screens/Analytics.jsx\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport {\r\n  Container,\r\n  Typography,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  Box,\r\n  IconButton,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  MenuItem,\r\n  Chip\r\n} from '@mui/material';\r\nimport {\r\n  Refresh,\r\n  TrendingUp,\r\n  Warning,\r\n  Assessment,\r\n  Timeline\r\n} from '@mui/icons-material';\r\nimport {\r\n  LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer,\r\n  BarChart, Bar\r\n} from 'recharts';\r\nimport '../App.css';\r\nimport { format } from 'date-fns';\r\nimport LoadingSpinner from '../components/LoadingSpinner';\r\nimport ErrorAlert from '../components/ErrorAlert';\r\nimport StatsCard from '../components/StatsCard';\r\nimport AnomalyChart from '../components/anomalychart';\r\nimport AnomalyHeatmap from '../components/anomalyheatmap';\r\nimport AnomalySnapshots from '../components/anomalysnapshots';\r\nimport AIFeedbackButtons from '../components/AIFeedbackButtons';\r\nimport useFeedbackState from '../hooks/useFeedbackState';\r\n\r\nexport default function Analytics() {\r\n  const [summary, setSummary] = useState([]);\r\n  const [errors, setErrors] = useState([]);\r\n  const [errorMsg, setErrorMsg] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [timeRange, setTimeRange] = useState('24h');\r\n\r\n  // Use the feedback state hook\r\n  const { feedbackStates, submitFeedback } = useFeedbackState('analytics');\r\n\r\n  useEffect(() => {\r\n    fetchData();\r\n  }, []);\r\n\r\n  const fetchData = async () => {\r\n    try {\r\n      setLoading(true);\r\n      // Fetch anomalies‐per‐minute summary\r\n      const res1 = await fetch('/predict/analytics/summary');\r\n      if (!res1.ok) throw new Error(`Summary HTTP ${res1.status}`);\r\n      const summaryJson = await res1.json();\r\n      if (summaryJson && typeof summaryJson === 'object' && !Array.isArray(summaryJson)) {\r\n        const summaryArr = Object.entries(summaryJson).map(([time, count]) => ({\r\n          time,\r\n          count,\r\n          formattedTime: format(new Date(time), 'HH:mm')\r\n        }));\r\n        setSummary(summaryArr);\r\n      } else {\r\n        setSummary([]);\r\n      }\r\n\r\n      // Fetch recent reconstruction errors\r\n      const res2 = await fetch('/predict/analytics/errors');\r\n      if (!res2.ok) throw new Error(`Errors HTTP ${res2.status}`);\r\n      const errorsJson = await res2.json();\r\n      if (Array.isArray(errorsJson)) {\r\n        setErrors(errorsJson);\r\n      } else {\r\n        setErrors([]);\r\n      }\r\n\r\n      setErrorMsg(null);\r\n    } catch (e) {\r\n      console.error('Analytics fetch error:', e);\r\n      setErrorMsg('Failed to load analytics data.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Calculate statistics\r\n  const totalAnomalies = summary.reduce((sum, item) => sum + item.count, 0);\r\n  const avgErrorRate = errors.length > 0 ? (errors.reduce((sum, err) => sum + err, 0) / errors.length).toFixed(4) : 0;\r\n  const maxAnomaliesPerMinute = summary.length > 0 ? Math.max(...summary.map(item => item.count)) : 0;\r\n\r\n  // Prepare error distribution data\r\n  const errorDistribution = errors.map((error, index) => ({\r\n    index: index + 1,\r\n    value: parseFloat(error.toFixed(4)),\r\n    category: error > 0.5 ? 'High' : error > 0.2 ? 'Medium' : 'Low'\r\n  }));\r\n\r\n  const handleFeedback = async (suggestionId, feedbackType, suggestionType) => {\r\n    await submitFeedback(suggestionId, feedbackType, suggestionType, {\r\n      analytics_data: {\r\n        total_anomalies: totalAnomalies,\r\n        avg_error_rate: avgErrorRate,\r\n        time_range: timeRange\r\n      }\r\n    });\r\n  };\r\n\r\n\r\n\r\n  if (loading) {\r\n    return <LoadingSpinner message=\"Loading analytics data...\" />;\r\n  }\r\n\r\n  return (\r\n    <Container maxWidth=\"xl\">\r\n      <Box mb={3}>\r\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom fontWeight=\"bold\">\r\n          Analytics Dashboard\r\n        </Typography>\r\n        <Typography variant=\"body1\" color=\"text.secondary\">\r\n          Real-time insights and anomaly detection metrics\r\n        </Typography>\r\n      </Box>\r\n\r\n      {/* Controls */}\r\n      <Box display=\"flex\" gap={2} mb={3} flexWrap=\"wrap\" alignItems=\"center\">\r\n        <FormControl size=\"small\" sx={{ minWidth: 120 }}>\r\n          <InputLabel>Time Range</InputLabel>\r\n          <Select\r\n            value={timeRange}\r\n            label=\"Time Range\"\r\n            onChange={(e) => setTimeRange(e.target.value)}\r\n          >\r\n            <MenuItem value=\"1h\">Last Hour</MenuItem>\r\n            <MenuItem value=\"24h\">Last 24 Hours</MenuItem>\r\n            <MenuItem value=\"7d\">Last 7 Days</MenuItem>\r\n          </Select>\r\n        </FormControl>\r\n        <IconButton onClick={fetchData} disabled={loading}>\r\n          <Refresh />\r\n        </IconButton>\r\n        <Chip\r\n          icon={<Assessment />}\r\n          label={`${summary.length} data points`}\r\n          color=\"primary\"\r\n          variant=\"outlined\"\r\n        />\r\n      </Box>\r\n\r\n      {errorMsg && (\r\n        <ErrorAlert\r\n          message={errorMsg}\r\n          onRetry={fetchData}\r\n          sx={{ mb: 3 }}\r\n        />\r\n      )}\r\n\r\n      {/* Statistics Cards */}\r\n      <Grid container spacing={3} mb={4}>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <StatsCard\r\n            title=\"Total Anomalies\"\r\n            value={totalAnomalies}\r\n            subtitle=\"Detected events\"\r\n            icon={<Warning />}\r\n            color=\"warning\"\r\n          />\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <StatsCard\r\n            title=\"Avg Error Rate\"\r\n            value={avgErrorRate}\r\n            subtitle=\"Reconstruction error\"\r\n            icon={<TrendingUp />}\r\n            color=\"error\"\r\n          />\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <StatsCard\r\n            title=\"Peak Activity\"\r\n            value={maxAnomaliesPerMinute}\r\n            subtitle=\"Max per minute\"\r\n            icon={<Timeline />}\r\n            color=\"info\"\r\n          />\r\n        </Grid>\r\n        <Grid item xs={12} sm={6} md={3}>\r\n          <StatsCard\r\n            title=\"Data Points\"\r\n            value={errors.length}\r\n            subtitle=\"Error samples\"\r\n            icon={<Assessment />}\r\n            color=\"primary\"\r\n          />\r\n        </Grid>\r\n      </Grid>\r\n\r\n      {/* AI Analytics Feedback Section */}\r\n      <Box mb={3}>\r\n        <Card\r\n          sx={{\r\n            p: 2,\r\n            backgroundColor: 'background.default',\r\n            border: feedbackStates['analytics_summary'] === 'accept'\r\n              ? '2px solid #10b981'\r\n              : feedbackStates['analytics_summary'] === 'reject'\r\n              ? '2px solid #ef4444'\r\n              : '1px solid',\r\n            borderColor: feedbackStates['analytics_summary'] ? 'transparent' : 'divider',\r\n            transition: 'all 0.3s ease'\r\n          }}\r\n        >\r\n          <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\" gap={2}>\r\n            <Box>\r\n              <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                  AI Analytics Insights\r\n                </Typography>\r\n                {feedbackStates['analytics_summary'] && (\r\n                  <Chip\r\n                    size=\"small\"\r\n                    label={feedbackStates['analytics_summary'] === 'accept' ? 'Helpful' : 'Needs improvement'}\r\n                    color={feedbackStates['analytics_summary'] === 'accept' ? 'success' : 'warning'}\r\n                    sx={{ fontSize: '0.7rem' }}\r\n                  />\r\n                )}\r\n              </Box>\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                These metrics are generated by our AI anomaly detection system\r\n              </Typography>\r\n            </Box>\r\n            <Box sx={{ minWidth: '300px' }}>\r\n              <AIFeedbackButtons\r\n                suggestionId=\"analytics_summary\"\r\n                suggestionType=\"analytics_insights\"\r\n                onFeedback={handleFeedback}\r\n                variant=\"default\"\r\n                initialFeedback={feedbackStates['analytics_summary']}\r\n              />\r\n            </Box>\r\n          </Box>\r\n        </Card>\r\n      </Box>\r\n\r\n      <Grid container spacing={3}>\r\n        {/* Anomalies Timeline Chart */}\r\n        <Grid item xs={12} lg={8}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Anomalies Timeline\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\" mb={2}>\r\n                Anomaly detections over time\r\n              </Typography>\r\n              <ResponsiveContainer width=\"100%\" height={350}>\r\n                <LineChart data={summary}>\r\n                  <XAxis\r\n                    dataKey=\"formattedTime\"\r\n                    tick={{ fontSize: 12 }}\r\n                  />\r\n                  <YAxis tick={{ fontSize: 12 }} />\r\n                  <Tooltip\r\n                    labelFormatter={(label, payload) => {\r\n                      if (payload && payload[0]) {\r\n                        return format(new Date(payload[0].payload.time), 'MMM dd, yyyy HH:mm');\r\n                      }\r\n                      return label;\r\n                    }}\r\n                  />\r\n                  <Line\r\n                    type=\"monotone\"\r\n                    dataKey=\"count\"\r\n                    stroke=\"#1976d2\"\r\n                    strokeWidth={2}\r\n                    dot={{ fill: '#1976d2', strokeWidth: 2, r: 4 }}\r\n                    activeDot={{ r: 6 }}\r\n                  />\r\n                </LineChart>\r\n              </ResponsiveContainer>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Error Distribution Chart */}\r\n        <Grid item xs={12} lg={4}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Error Distribution\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\" mb={2}>\r\n                Reconstruction error levels\r\n              </Typography>\r\n              <ResponsiveContainer width=\"100%\" height={350}>\r\n                <BarChart data={errorDistribution}>\r\n                  <XAxis dataKey=\"index\" tick={{ fontSize: 10 }} />\r\n                  <YAxis tick={{ fontSize: 10 }} />\r\n                  <Tooltip\r\n                    formatter={(value) => [value.toFixed(4), 'Error Rate']}\r\n                  />\r\n                  <Bar\r\n                    dataKey=\"value\"\r\n                    fill=\"#1976d2\"\r\n                    radius={[2, 2, 0, 0]}\r\n                  />\r\n                </BarChart>\r\n              </ResponsiveContainer>\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        {/* Real-time Anomaly Charts - Your Custom Components */}\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\r\n                <Typography variant=\"h6\">\r\n                  Security Anomaly Chart\r\n                </Typography>\r\n                <Box sx={{\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  gap: 1,\r\n                  p: 1,\r\n                  backgroundColor: 'background.paper',\r\n                  borderRadius: 1,\r\n                  border: '1px solid',\r\n                  borderColor: 'divider'\r\n                }}>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\" fontWeight={500}>\r\n                    Was this chart helpful?\r\n                  </Typography>\r\n                  <AIFeedbackButtons\r\n                    suggestionId=\"anomaly_chart\"\r\n                    suggestionType=\"anomaly_visualization\"\r\n                    onFeedback={handleFeedback}\r\n                    variant=\"compact\"\r\n                    initialFeedback={feedbackStates['anomaly_chart']}\r\n                  />\r\n                </Box>\r\n              </Box>\r\n              <AnomalyChart />\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        <Grid item xs={12} lg={6}>\r\n          <Card>\r\n            <CardContent>\r\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\r\n                <Typography variant=\"h6\">\r\n                  Activity Heatmap\r\n                </Typography>\r\n                <AIFeedbackButtons\r\n                  suggestionId=\"activity_heatmap\"\r\n                  suggestionType=\"heatmap_visualization\"\r\n                  onFeedback={handleFeedback}\r\n                  variant=\"compact\"\r\n                  initialFeedback={feedbackStates['activity_heatmap']}\r\n                />\r\n              </Box>\r\n              <AnomalyHeatmap />\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n\r\n        <Grid item xs={12}>\r\n          <Card>\r\n            <CardContent>\r\n              <Typography variant=\"h6\" gutterBottom>\r\n                Snapshot of Anomalies\r\n              </Typography>\r\n              <AnomalySnapshots />\r\n            </CardContent>\r\n          </Card>\r\n        </Grid>\r\n      </Grid>\r\n    </Container>\r\n  );\r\n}\r\n\r\n"], "mappings": ";;AAAA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,GAAG,EACHC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,QACC,eAAe;AACtB,SACEC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,QAAQ,QACH,qBAAqB;AAC5B,SACEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,mBAAmB,EAC3DC,QAAQ,EAAEC,GAAG,QACR,UAAU;AACjB,OAAO,YAAY;AACnB,SAASC,MAAM,QAAQ,UAAU;AACjC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,gBAAgB,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,eAAe,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAClC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0C,MAAM,EAAEC,SAAS,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgD,SAAS,EAAEC,YAAY,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAM;IAAEkD,cAAc;IAAEC;EAAe,CAAC,GAAGhB,gBAAgB,CAAC,WAAW,CAAC;EAExElC,SAAS,CAAC,MAAM;IACdmD,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMM,IAAI,GAAG,MAAMC,KAAK,CAAC,4BAA4B,CAAC;MACtD,IAAI,CAACD,IAAI,CAACE,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,gBAAgBH,IAAI,CAACI,MAAM,EAAE,CAAC;MAC5D,MAAMC,WAAW,GAAG,MAAML,IAAI,CAACM,IAAI,CAAC,CAAC;MACrC,IAAID,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,EAAE;QACjF,MAAMI,UAAU,GAAGC,MAAM,CAACC,OAAO,CAACN,WAAW,CAAC,CAACO,GAAG,CAAC,CAAC,CAACC,IAAI,EAAEC,KAAK,CAAC,MAAM;UACrED,IAAI;UACJC,KAAK;UACLC,aAAa,EAAEzC,MAAM,CAAC,IAAI0C,IAAI,CAACH,IAAI,CAAC,EAAE,OAAO;QAC/C,CAAC,CAAC,CAAC;QACHzB,UAAU,CAACqB,UAAU,CAAC;MACxB,CAAC,MAAM;QACLrB,UAAU,CAAC,EAAE,CAAC;MAChB;;MAEA;MACA,MAAM6B,IAAI,GAAG,MAAMhB,KAAK,CAAC,2BAA2B,CAAC;MACrD,IAAI,CAACgB,IAAI,CAACf,EAAE,EAAE,MAAM,IAAIC,KAAK,CAAC,eAAec,IAAI,CAACb,MAAM,EAAE,CAAC;MAC3D,MAAMc,UAAU,GAAG,MAAMD,IAAI,CAACX,IAAI,CAAC,CAAC;MACpC,IAAIC,KAAK,CAACC,OAAO,CAACU,UAAU,CAAC,EAAE;QAC7B5B,SAAS,CAAC4B,UAAU,CAAC;MACvB,CAAC,MAAM;QACL5B,SAAS,CAAC,EAAE,CAAC;MACf;MAEAE,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC,CAAC,OAAO2B,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEF,CAAC,CAAC;MAC1C3B,WAAW,CAAC,gCAAgC,CAAC;IAC/C,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4B,cAAc,GAAGnC,OAAO,CAACoC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACX,KAAK,EAAE,CAAC,CAAC;EACzE,MAAMY,YAAY,GAAGrC,MAAM,CAACsC,MAAM,GAAG,CAAC,GAAG,CAACtC,MAAM,CAACkC,MAAM,CAAC,CAACC,GAAG,EAAEI,GAAG,KAAKJ,GAAG,GAAGI,GAAG,EAAE,CAAC,CAAC,GAAGvC,MAAM,CAACsC,MAAM,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EACnH,MAAMC,qBAAqB,GAAG3C,OAAO,CAACwC,MAAM,GAAG,CAAC,GAAGI,IAAI,CAACC,GAAG,CAAC,GAAG7C,OAAO,CAACyB,GAAG,CAACa,IAAI,IAAIA,IAAI,CAACX,KAAK,CAAC,CAAC,GAAG,CAAC;;EAEnG;EACA,MAAMmB,iBAAiB,GAAG5C,MAAM,CAACuB,GAAG,CAAC,CAACS,KAAK,EAAEa,KAAK,MAAM;IACtDA,KAAK,EAAEA,KAAK,GAAG,CAAC;IAChBC,KAAK,EAAEC,UAAU,CAACf,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC;IACnCQ,QAAQ,EAAEhB,KAAK,GAAG,GAAG,GAAG,MAAM,GAAGA,KAAK,GAAG,GAAG,GAAG,QAAQ,GAAG;EAC5D,CAAC,CAAC,CAAC;EAEH,MAAMiB,cAAc,GAAG,MAAAA,CAAOC,YAAY,EAAEC,YAAY,EAAEC,cAAc,KAAK;IAC3E,MAAM3C,cAAc,CAACyC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAE;MAC/DC,cAAc,EAAE;QACdC,eAAe,EAAErB,cAAc;QAC/BsB,cAAc,EAAElB,YAAY;QAC5BmB,UAAU,EAAElD;MACd;IACF,CAAC,CAAC;EACJ,CAAC;EAID,IAAIF,OAAO,EAAE;IACX,oBAAOT,OAAA,CAACT,cAAc;MAACuE,OAAO,EAAC;IAA2B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC/D;EAEA,oBACElE,OAAA,CAACnC,SAAS;IAACsG,QAAQ,EAAC,IAAI;IAAAC,QAAA,gBACtBpE,OAAA,CAAC9B,GAAG;MAACmG,EAAE,EAAE,CAAE;MAAAD,QAAA,gBACTpE,OAAA,CAAClC,UAAU;QAACwG,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAACC,UAAU,EAAC,MAAM;QAAAL,QAAA,EAAC;MAEvE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACblE,OAAA,CAAClC,UAAU;QAACwG,OAAO,EAAC,OAAO;QAACI,KAAK,EAAC,gBAAgB;QAAAN,QAAA,EAAC;MAEnD;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNlE,OAAA,CAAC9B,GAAG;MAACyG,OAAO,EAAC,MAAM;MAACC,GAAG,EAAE,CAAE;MAACP,EAAE,EAAE,CAAE;MAACQ,QAAQ,EAAC,MAAM;MAACC,UAAU,EAAC,QAAQ;MAAAV,QAAA,gBACpEpE,OAAA,CAAC5B,WAAW;QAAC2G,IAAI,EAAC,OAAO;QAACC,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAb,QAAA,gBAC9CpE,OAAA,CAAC3B,UAAU;UAAA+F,QAAA,EAAC;QAAU;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACnClE,OAAA,CAAC1B,MAAM;UACL6E,KAAK,EAAExC,SAAU;UACjBuE,KAAK,EAAC,YAAY;UAClBC,QAAQ,EAAGhD,CAAC,IAAKvB,YAAY,CAACuB,CAAC,CAACiD,MAAM,CAACjC,KAAK,CAAE;UAAAiB,QAAA,gBAE9CpE,OAAA,CAACzB,QAAQ;YAAC4E,KAAK,EAAC,IAAI;YAAAiB,QAAA,EAAC;UAAS;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACzClE,OAAA,CAACzB,QAAQ;YAAC4E,KAAK,EAAC,KAAK;YAAAiB,QAAA,EAAC;UAAa;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC9ClE,OAAA,CAACzB,QAAQ;YAAC4E,KAAK,EAAC,IAAI;YAAAiB,QAAA,EAAC;UAAW;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACdlE,OAAA,CAAC7B,UAAU;QAACkH,OAAO,EAAEtE,SAAU;QAACuE,QAAQ,EAAE7E,OAAQ;QAAA2D,QAAA,eAChDpE,OAAA,CAACvB,OAAO;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACblE,OAAA,CAACxB,IAAI;QACH+G,IAAI,eAAEvF,OAAA,CAACpB,UAAU;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACrBgB,KAAK,EAAE,GAAG/E,OAAO,CAACwC,MAAM,cAAe;QACvC+B,KAAK,EAAC,SAAS;QACfJ,OAAO,EAAC;MAAU;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEL3D,QAAQ,iBACPP,OAAA,CAACR,UAAU;MACTsE,OAAO,EAAEvD,QAAS;MAClBiF,OAAO,EAAEzE,SAAU;MACnBiE,EAAE,EAAE;QAAEX,EAAE,EAAE;MAAE;IAAE;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CACF,eAGDlE,OAAA,CAACjC,IAAI;MAAC0H,SAAS;MAACC,OAAO,EAAE,CAAE;MAACrB,EAAE,EAAE,CAAE;MAAAD,QAAA,gBAChCpE,OAAA,CAACjC,IAAI;QAAC0E,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eAC9BpE,OAAA,CAACP,SAAS;UACRqG,KAAK,EAAC,iBAAiB;UACvB3C,KAAK,EAAEb,cAAe;UACtByD,QAAQ,EAAC,iBAAiB;UAC1BR,IAAI,eAAEvF,OAAA,CAACrB,OAAO;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAClBQ,KAAK,EAAC;QAAS;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPlE,OAAA,CAACjC,IAAI;QAAC0E,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eAC9BpE,OAAA,CAACP,SAAS;UACRqG,KAAK,EAAC,gBAAgB;UACtB3C,KAAK,EAAET,YAAa;UACpBqD,QAAQ,EAAC,sBAAsB;UAC/BR,IAAI,eAAEvF,OAAA,CAACtB,UAAU;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBQ,KAAK,EAAC;QAAO;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPlE,OAAA,CAACjC,IAAI;QAAC0E,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eAC9BpE,OAAA,CAACP,SAAS;UACRqG,KAAK,EAAC,eAAe;UACrB3C,KAAK,EAAEL,qBAAsB;UAC7BiD,QAAQ,EAAC,gBAAgB;UACzBR,IAAI,eAAEvF,OAAA,CAACnB,QAAQ;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAM;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPlE,OAAA,CAACjC,IAAI;QAAC0E,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eAC9BpE,OAAA,CAACP,SAAS;UACRqG,KAAK,EAAC,aAAa;UACnB3C,KAAK,EAAE9C,MAAM,CAACsC,MAAO;UACrBoD,QAAQ,EAAC,eAAe;UACxBR,IAAI,eAAEvF,OAAA,CAACpB,UAAU;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACrBQ,KAAK,EAAC;QAAS;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPlE,OAAA,CAAC9B,GAAG;MAACmG,EAAE,EAAE,CAAE;MAAAD,QAAA,eACTpE,OAAA,CAAChC,IAAI;QACHgH,EAAE,EAAE;UACFgB,CAAC,EAAE,CAAC;UACJC,eAAe,EAAE,oBAAoB;UACrCC,MAAM,EAAErF,cAAc,CAAC,mBAAmB,CAAC,KAAK,QAAQ,GACpD,mBAAmB,GACnBA,cAAc,CAAC,mBAAmB,CAAC,KAAK,QAAQ,GAChD,mBAAmB,GACnB,WAAW;UACfsF,WAAW,EAAEtF,cAAc,CAAC,mBAAmB,CAAC,GAAG,aAAa,GAAG,SAAS;UAC5EuF,UAAU,EAAE;QACd,CAAE;QAAAhC,QAAA,eAEFpE,OAAA,CAAC9B,GAAG;UAACyG,OAAO,EAAC,MAAM;UAACG,UAAU,EAAC,QAAQ;UAACuB,cAAc,EAAC,eAAe;UAACxB,QAAQ,EAAC,MAAM;UAACD,GAAG,EAAE,CAAE;UAAAR,QAAA,gBAC5FpE,OAAA,CAAC9B,GAAG;YAAAkG,QAAA,gBACFpE,OAAA,CAAC9B,GAAG;cAACyG,OAAO,EAAC,MAAM;cAACG,UAAU,EAAC,QAAQ;cAACF,GAAG,EAAE,CAAE;cAAAR,QAAA,gBAC7CpE,OAAA,CAAClC,UAAU;gBAACwG,OAAO,EAAC,IAAI;gBAACE,YAAY;gBAAAJ,QAAA,EAAC;cAEtC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZrD,cAAc,CAAC,mBAAmB,CAAC,iBAClCb,OAAA,CAACxB,IAAI;gBACHuG,IAAI,EAAC,OAAO;gBACZG,KAAK,EAAErE,cAAc,CAAC,mBAAmB,CAAC,KAAK,QAAQ,GAAG,SAAS,GAAG,mBAAoB;gBAC1F6D,KAAK,EAAE7D,cAAc,CAAC,mBAAmB,CAAC,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;gBAChFmE,EAAE,EAAE;kBAAEsB,QAAQ,EAAE;gBAAS;cAAE;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNlE,OAAA,CAAClC,UAAU;cAACwG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAAAN,QAAA,EAAC;YAEnD;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNlE,OAAA,CAAC9B,GAAG;YAAC8G,EAAE,EAAE;cAAEC,QAAQ,EAAE;YAAQ,CAAE;YAAAb,QAAA,eAC7BpE,OAAA,CAACH,iBAAiB;cAChB0D,YAAY,EAAC,mBAAmB;cAChCE,cAAc,EAAC,oBAAoB;cACnC8C,UAAU,EAAEjD,cAAe;cAC3BgB,OAAO,EAAC,SAAS;cACjBkC,eAAe,EAAE3F,cAAc,CAAC,mBAAmB;YAAE;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENlE,OAAA,CAACjC,IAAI;MAAC0H,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAtB,QAAA,gBAEzBpE,OAAA,CAACjC,IAAI;QAAC0E,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACc,EAAE,EAAE,CAAE;QAAArC,QAAA,eACvBpE,OAAA,CAAChC,IAAI;UAAAoG,QAAA,eACHpE,OAAA,CAAC/B,WAAW;YAAAmG,QAAA,gBACVpE,OAAA,CAAClC,UAAU;cAACwG,OAAO,EAAC,IAAI;cAACE,YAAY;cAAAJ,QAAA,EAAC;YAEtC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblE,OAAA,CAAClC,UAAU;cAACwG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAACL,EAAE,EAAE,CAAE;cAAAD,QAAA,EAAC;YAE1D;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblE,OAAA,CAACb,mBAAmB;cAACuH,KAAK,EAAC,MAAM;cAACC,MAAM,EAAE,GAAI;cAAAvC,QAAA,eAC5CpE,OAAA,CAAClB,SAAS;gBAAC8H,IAAI,EAAEzG,OAAQ;gBAAAiE,QAAA,gBACvBpE,OAAA,CAAChB,KAAK;kBACJ6H,OAAO,EAAC,eAAe;kBACvBC,IAAI,EAAE;oBAAER,QAAQ,EAAE;kBAAG;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFlE,OAAA,CAACf,KAAK;kBAAC6H,IAAI,EAAE;oBAAER,QAAQ,EAAE;kBAAG;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjClE,OAAA,CAACd,OAAO;kBACN6H,cAAc,EAAEA,CAAC7B,KAAK,EAAE8B,OAAO,KAAK;oBAClC,IAAIA,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,EAAE;sBACzB,OAAO1H,MAAM,CAAC,IAAI0C,IAAI,CAACgF,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAACnF,IAAI,CAAC,EAAE,oBAAoB,CAAC;oBACxE;oBACA,OAAOqD,KAAK;kBACd;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFlE,OAAA,CAACjB,IAAI;kBACHkI,IAAI,EAAC,UAAU;kBACfJ,OAAO,EAAC,OAAO;kBACfK,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAE,CAAE;kBACfC,GAAG,EAAE;oBAAEC,IAAI,EAAE,SAAS;oBAAEF,WAAW,EAAE,CAAC;oBAAEG,CAAC,EAAE;kBAAE,CAAE;kBAC/CC,SAAS,EAAE;oBAAED,CAAC,EAAE;kBAAE;gBAAE;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPlE,OAAA,CAACjC,IAAI;QAAC0E,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACc,EAAE,EAAE,CAAE;QAAArC,QAAA,eACvBpE,OAAA,CAAChC,IAAI;UAAAoG,QAAA,eACHpE,OAAA,CAAC/B,WAAW;YAAAmG,QAAA,gBACVpE,OAAA,CAAClC,UAAU;cAACwG,OAAO,EAAC,IAAI;cAACE,YAAY;cAAAJ,QAAA,EAAC;YAEtC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblE,OAAA,CAAClC,UAAU;cAACwG,OAAO,EAAC,OAAO;cAACI,KAAK,EAAC,gBAAgB;cAACL,EAAE,EAAE,CAAE;cAAAD,QAAA,EAAC;YAE1D;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblE,OAAA,CAACb,mBAAmB;cAACuH,KAAK,EAAC,MAAM;cAACC,MAAM,EAAE,GAAI;cAAAvC,QAAA,eAC5CpE,OAAA,CAACZ,QAAQ;gBAACwH,IAAI,EAAE3D,iBAAkB;gBAAAmB,QAAA,gBAChCpE,OAAA,CAAChB,KAAK;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,IAAI,EAAE;oBAAER,QAAQ,EAAE;kBAAG;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDlE,OAAA,CAACf,KAAK;kBAAC6H,IAAI,EAAE;oBAAER,QAAQ,EAAE;kBAAG;gBAAE;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjClE,OAAA,CAACd,OAAO;kBACNsI,SAAS,EAAGrE,KAAK,IAAK,CAACA,KAAK,CAACN,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY;gBAAE;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACFlE,OAAA,CAACX,GAAG;kBACFwH,OAAO,EAAC,OAAO;kBACfQ,IAAI,EAAC,SAAS;kBACdI,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;gBAAE;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGPlE,OAAA,CAACjC,IAAI;QAAC0E,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACc,EAAE,EAAE,CAAE;QAAArC,QAAA,eACvBpE,OAAA,CAAChC,IAAI;UAAAoG,QAAA,eACHpE,OAAA,CAAC/B,WAAW;YAAAmG,QAAA,gBACVpE,OAAA,CAAC9B,GAAG;cAACyG,OAAO,EAAC,MAAM;cAAC0B,cAAc,EAAC,eAAe;cAACvB,UAAU,EAAC,QAAQ;cAACT,EAAE,EAAE,CAAE;cAAAD,QAAA,gBAC3EpE,OAAA,CAAClC,UAAU;gBAACwG,OAAO,EAAC,IAAI;gBAAAF,QAAA,EAAC;cAEzB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblE,OAAA,CAAC9B,GAAG;gBAAC8G,EAAE,EAAE;kBACPL,OAAO,EAAE,MAAM;kBACfG,UAAU,EAAE,QAAQ;kBACpBF,GAAG,EAAE,CAAC;kBACNoB,CAAC,EAAE,CAAC;kBACJC,eAAe,EAAE,kBAAkB;kBACnCyB,YAAY,EAAE,CAAC;kBACfxB,MAAM,EAAE,WAAW;kBACnBC,WAAW,EAAE;gBACf,CAAE;gBAAA/B,QAAA,gBACApE,OAAA,CAAClC,UAAU;kBAACwG,OAAO,EAAC,OAAO;kBAACI,KAAK,EAAC,gBAAgB;kBAACD,UAAU,EAAE,GAAI;kBAAAL,QAAA,EAAC;gBAEpE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACblE,OAAA,CAACH,iBAAiB;kBAChB0D,YAAY,EAAC,eAAe;kBAC5BE,cAAc,EAAC,uBAAuB;kBACtC8C,UAAU,EAAEjD,cAAe;kBAC3BgB,OAAO,EAAC,SAAS;kBACjBkC,eAAe,EAAE3F,cAAc,CAAC,eAAe;gBAAE;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlE,OAAA,CAACN,YAAY;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPlE,OAAA,CAACjC,IAAI;QAAC0E,IAAI;QAACkD,EAAE,EAAE,EAAG;QAACc,EAAE,EAAE,CAAE;QAAArC,QAAA,eACvBpE,OAAA,CAAChC,IAAI;UAAAoG,QAAA,eACHpE,OAAA,CAAC/B,WAAW;YAAAmG,QAAA,gBACVpE,OAAA,CAAC9B,GAAG;cAACyG,OAAO,EAAC,MAAM;cAAC0B,cAAc,EAAC,eAAe;cAACvB,UAAU,EAAC,QAAQ;cAACT,EAAE,EAAE,CAAE;cAAAD,QAAA,gBAC3EpE,OAAA,CAAClC,UAAU;gBAACwG,OAAO,EAAC,IAAI;gBAAAF,QAAA,EAAC;cAEzB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblE,OAAA,CAACH,iBAAiB;gBAChB0D,YAAY,EAAC,kBAAkB;gBAC/BE,cAAc,EAAC,uBAAuB;gBACtC8C,UAAU,EAAEjD,cAAe;gBAC3BgB,OAAO,EAAC,SAAS;gBACjBkC,eAAe,EAAE3F,cAAc,CAAC,kBAAkB;cAAE;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlE,OAAA,CAACL,cAAc;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPlE,OAAA,CAACjC,IAAI;QAAC0E,IAAI;QAACkD,EAAE,EAAE,EAAG;QAAAvB,QAAA,eAChBpE,OAAA,CAAChC,IAAI;UAAAoG,QAAA,eACHpE,OAAA,CAAC/B,WAAW;YAAAmG,QAAA,gBACVpE,OAAA,CAAClC,UAAU;cAACwG,OAAO,EAAC,IAAI;cAACE,YAAY;cAAAJ,QAAA,EAAC;YAEtC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblE,OAAA,CAACJ,gBAAgB;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB;AAAChE,EAAA,CA1VuBD,SAAS;EAAA,QAQYH,gBAAgB;AAAA;AAAA6H,EAAA,GARrC1H,SAAS;AAAA,IAAA0H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}