{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useId from '@mui/utils/useId';\nimport capitalize from \"../utils/capitalize.js\";\nimport Modal from \"../Modal/index.js\";\nimport Fade from \"../Fade/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport dialogClasses, { getDialogUtilityClass } from \"./dialogClasses.js\";\nimport DialogContext from \"./DialogContext.js\";\nimport Backdrop from \"../Backdrop/index.js\";\nimport { styled, useTheme } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DialogBackdrop = styled(Backdrop, {\n  name: 'MuiDialog',\n  slot: 'Backdrop',\n  overrides: (props, styles) => styles.backdrop\n})({\n  // Improve scrollable dialog support.\n  zIndex: -1\n});\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    scroll,\n    maxWidth,\n    fullWidth,\n    fullScreen\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    container: ['container', `scroll${capitalize(scroll)}`],\n    paper: ['paper', `paperScroll${capitalize(scroll)}`, `paperWidth${capitalize(String(maxWidth))}`, fullWidth && 'paperFullWidth', fullScreen && 'paperFullScreen']\n  };\n  return composeClasses(slots, getDialogUtilityClass, classes);\n};\nconst DialogRoot = styled(Modal, {\n  name: 'MuiDialog',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  '@media print': {\n    // Use !important to override the Modal inline-style.\n    position: 'absolute !important'\n  }\n});\nconst DialogContainer = styled('div', {\n  name: 'MuiDialog',\n  slot: 'Container',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.container, styles[`scroll${capitalize(ownerState.scroll)}`]];\n  }\n})({\n  height: '100%',\n  '@media print': {\n    height: 'auto'\n  },\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0,\n  variants: [{\n    props: {\n      scroll: 'paper'\n    },\n    style: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center'\n    }\n  }, {\n    props: {\n      scroll: 'body'\n    },\n    style: {\n      overflowY: 'auto',\n      overflowX: 'hidden',\n      textAlign: 'center',\n      '&::after': {\n        content: '\"\"',\n        display: 'inline-block',\n        verticalAlign: 'middle',\n        height: '100%',\n        width: '0'\n      }\n    }\n  }]\n});\nconst DialogPaper = styled(Paper, {\n  name: 'MuiDialog',\n  slot: 'Paper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.paper, styles[`scrollPaper${capitalize(ownerState.scroll)}`], styles[`paperWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fullWidth && styles.paperFullWidth, ownerState.fullScreen && styles.paperFullScreen];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  margin: 32,\n  position: 'relative',\n  overflowY: 'auto',\n  '@media print': {\n    overflowY: 'visible',\n    boxShadow: 'none'\n  },\n  variants: [{\n    props: {\n      scroll: 'paper'\n    },\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      maxHeight: 'calc(100% - 64px)'\n    }\n  }, {\n    props: {\n      scroll: 'body'\n    },\n    style: {\n      display: 'inline-block',\n      verticalAlign: 'middle',\n      textAlign: 'initial'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.maxWidth,\n    style: {\n      maxWidth: 'calc(100% - 64px)'\n    }\n  }, {\n    props: {\n      maxWidth: 'xs'\n    },\n    style: {\n      maxWidth: theme.breakpoints.unit === 'px' ? Math.max(theme.breakpoints.values.xs, 444) : `max(${theme.breakpoints.values.xs}${theme.breakpoints.unit}, 444px)`,\n      [`&.${dialogClasses.paperScrollBody}`]: {\n        [theme.breakpoints.down(Math.max(theme.breakpoints.values.xs, 444) + 32 * 2)]: {\n          maxWidth: 'calc(100% - 64px)'\n        }\n      }\n    }\n  }, ...Object.keys(theme.breakpoints.values).filter(maxWidth => maxWidth !== 'xs').map(maxWidth => ({\n    props: {\n      maxWidth\n    },\n    style: {\n      maxWidth: `${theme.breakpoints.values[maxWidth]}${theme.breakpoints.unit}`,\n      [`&.${dialogClasses.paperScrollBody}`]: {\n        [theme.breakpoints.down(theme.breakpoints.values[maxWidth] + 32 * 2)]: {\n          maxWidth: 'calc(100% - 64px)'\n        }\n      }\n    }\n  })), {\n    props: ({\n      ownerState\n    }) => ownerState.fullWidth,\n    style: {\n      width: 'calc(100% - 64px)'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.fullScreen,\n    style: {\n      margin: 0,\n      width: '100%',\n      maxWidth: '100%',\n      height: '100%',\n      maxHeight: 'none',\n      borderRadius: 0,\n      [`&.${dialogClasses.paperScrollBody}`]: {\n        margin: 0,\n        maxWidth: '100%'\n      }\n    }\n  }]\n})));\n\n/**\n * Dialogs are overlaid modal paper based components with a backdrop.\n */\nconst Dialog = /*#__PURE__*/React.forwardRef(function Dialog(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialog'\n  });\n  const theme = useTheme();\n  const defaultTransitionDuration = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n    'aria-describedby': ariaDescribedby,\n    'aria-labelledby': ariaLabelledbyProp,\n    'aria-modal': ariaModal = true,\n    BackdropComponent,\n    BackdropProps,\n    children,\n    className,\n    disableEscapeKeyDown = false,\n    fullScreen = false,\n    fullWidth = false,\n    maxWidth = 'sm',\n    onBackdropClick,\n    onClick,\n    onClose,\n    open,\n    PaperComponent = Paper,\n    PaperProps = {},\n    scroll = 'paper',\n    slots = {},\n    slotProps = {},\n    TransitionComponent = Fade,\n    transitionDuration = defaultTransitionDuration,\n    TransitionProps,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    disableEscapeKeyDown,\n    fullScreen,\n    fullWidth,\n    maxWidth,\n    scroll\n  };\n  const classes = useUtilityClasses(ownerState);\n  const backdropClick = React.useRef();\n  const handleMouseDown = event => {\n    // We don't want to close the dialog when clicking the dialog content.\n    // Make sure the event starts and ends on the same DOM element.\n    backdropClick.current = event.target === event.currentTarget;\n  };\n  const handleBackdropClick = event => {\n    if (onClick) {\n      onClick(event);\n    }\n\n    // Ignore the events not coming from the \"backdrop\".\n    if (!backdropClick.current) {\n      return;\n    }\n    backdropClick.current = null;\n    if (onBackdropClick) {\n      onBackdropClick(event);\n    }\n    if (onClose) {\n      onClose(event, 'backdropClick');\n    }\n  };\n  const ariaLabelledby = useId(ariaLabelledbyProp);\n  const dialogContextValue = React.useMemo(() => {\n    return {\n      titleId: ariaLabelledby\n    };\n  }, [ariaLabelledby]);\n  const backwardCompatibleSlots = {\n    transition: TransitionComponent,\n    ...slots\n  };\n  const backwardCompatibleSlotProps = {\n    transition: TransitionProps,\n    paper: PaperProps,\n    backdrop: BackdropProps,\n    ...slotProps\n  };\n  const externalForwardedProps = {\n    slots: backwardCompatibleSlots,\n    slotProps: backwardCompatibleSlotProps\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    elementType: DialogRoot,\n    shouldForwardComponentProp: true,\n    externalForwardedProps,\n    ownerState,\n    className: clsx(classes.root, className),\n    ref\n  });\n  const [BackdropSlot, backdropSlotProps] = useSlot('backdrop', {\n    elementType: DialogBackdrop,\n    shouldForwardComponentProp: true,\n    externalForwardedProps,\n    ownerState\n  });\n  const [PaperSlot, paperSlotProps] = useSlot('paper', {\n    elementType: DialogPaper,\n    shouldForwardComponentProp: true,\n    externalForwardedProps,\n    ownerState,\n    className: clsx(classes.paper, PaperProps.className)\n  });\n  const [ContainerSlot, containerSlotProps] = useSlot('container', {\n    elementType: DialogContainer,\n    externalForwardedProps,\n    ownerState,\n    className: clsx(classes.container)\n  });\n  const [TransitionSlot, transitionSlotProps] = useSlot('transition', {\n    elementType: Fade,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      appear: true,\n      in: open,\n      timeout: transitionDuration,\n      role: 'presentation'\n    }\n  });\n  return /*#__PURE__*/_jsx(RootSlot, {\n    closeAfterTransition: true,\n    slots: {\n      backdrop: BackdropSlot\n    },\n    slotProps: {\n      backdrop: {\n        transitionDuration,\n        as: BackdropComponent,\n        ...backdropSlotProps\n      }\n    },\n    disableEscapeKeyDown: disableEscapeKeyDown,\n    onClose: onClose,\n    open: open,\n    onClick: handleBackdropClick,\n    ...rootSlotProps,\n    ...other,\n    children: /*#__PURE__*/_jsx(TransitionSlot, {\n      ...transitionSlotProps,\n      children: /*#__PURE__*/_jsx(ContainerSlot, {\n        onMouseDown: handleMouseDown,\n        ...containerSlotProps,\n        children: /*#__PURE__*/_jsx(PaperSlot, {\n          as: PaperComponent,\n          elevation: 24,\n          role: \"dialog\",\n          \"aria-describedby\": ariaDescribedby,\n          \"aria-labelledby\": ariaLabelledby,\n          \"aria-modal\": ariaModal,\n          ...paperSlotProps,\n          children: /*#__PURE__*/_jsx(DialogContext.Provider, {\n            value: dialogContextValue,\n            children: children\n          })\n        })\n      })\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Dialog.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The id(s) of the element(s) that describe the dialog.\n   */\n  'aria-describedby': PropTypes.string,\n  /**\n   * The id(s) of the element(s) that label the dialog.\n   */\n  'aria-labelledby': PropTypes.string,\n  /**\n   * Informs assistive technologies that the element is modal.\n   * It's added on the element with role=\"dialog\".\n   * @default true\n   */\n  'aria-modal': PropTypes.oneOfType([PropTypes.oneOf(['false', 'true']), PropTypes.bool]),\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. While this prop currently works, it will be removed in the next major version.\n   * Use the `slots.backdrop` prop to make your application ready for the next version of Material UI.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   *   overridesResolver: (props, styles) => {\n   *     return styles.backdrop;\n   *   },\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * @ignore\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * Dialog children, usually the included sub-components.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, hitting escape will not fire the `onClose` callback.\n   * @default false\n   */\n  disableEscapeKeyDown: PropTypes.bool,\n  /**\n   * If `true`, the dialog is full-screen.\n   * @default false\n   */\n  fullScreen: PropTypes.bool,\n  /**\n   * If `true`, the dialog stretches to `maxWidth`.\n   *\n   * Notice that the dialog width grow is limited by the default margin.\n   * @default false\n   */\n  fullWidth: PropTypes.bool,\n  /**\n   * Determine the max-width of the dialog.\n   * The dialog width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'sm'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * Callback fired when the backdrop is clicked.\n   * @deprecated Use the `onClose` prop with the `reason` argument to handle the `backdropClick` events.\n   */\n  onBackdropClick: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The component used to render the body of the dialog.\n   * @default Paper\n   */\n  PaperComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Paper`](https://mui.com/material-ui/api/paper/) element.\n   * @default {}\n   * @deprecated Use `slotProps.paper` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  PaperProps: PropTypes.object,\n  /**\n   * Determine the container for scrolling the dialog.\n   * @default 'paper'\n   */\n  scroll: PropTypes.oneOf(['body', 'paper']),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    container: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    container: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Fade\n   * @deprecated Use `slots.transition` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @deprecated Use `slotProps.transition` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Dialog;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "useId", "capitalize", "Modal", "Fade", "Paper", "dialogClasses", "getDialogUtilityClass", "DialogContext", "Backdrop", "styled", "useTheme", "memoTheme", "useDefaultProps", "useSlot", "jsx", "_jsx", "DialogBackdrop", "name", "slot", "overrides", "props", "styles", "backdrop", "zIndex", "useUtilityClasses", "ownerState", "classes", "scroll", "max<PERSON><PERSON><PERSON>", "fullWidth", "fullScreen", "slots", "root", "container", "paper", "String", "DialogRoot", "overridesResolver", "position", "DialogContainer", "height", "outline", "variants", "style", "display", "justifyContent", "alignItems", "overflowY", "overflowX", "textAlign", "content", "verticalAlign", "width", "DialogPaper", "paperFullWidth", "paperFullScreen", "theme", "margin", "boxShadow", "flexDirection", "maxHeight", "breakpoints", "unit", "Math", "max", "values", "xs", "paperScrollBody", "down", "Object", "keys", "filter", "map", "borderRadius", "Dialog", "forwardRef", "inProps", "ref", "defaultTransitionDuration", "enter", "transitions", "duration", "enteringScreen", "exit", "leavingScreen", "aria<PERSON><PERSON><PERSON><PERSON>", "ariaLabelledbyProp", "ariaModal", "BackdropComponent", "BackdropProps", "children", "className", "disableEscapeKeyDown", "onBackdropClick", "onClick", "onClose", "open", "PaperComponent", "PaperProps", "slotProps", "TransitionComponent", "transitionDuration", "TransitionProps", "other", "backdropClick", "useRef", "handleMouseDown", "event", "current", "target", "currentTarget", "handleBackdropClick", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dialogContextValue", "useMemo", "titleId", "backwardCompatibleSlots", "transition", "backwardCompatibleSlotProps", "externalForwardedProps", "RootSlot", "rootSlotProps", "elementType", "shouldForwardComponentProp", "BackdropSlot", "backdropSlotProps", "PaperSlot", "paperSlotProps", "ContainerSlot", "containerSlotProps", "TransitionSlot", "transitionSlotProps", "additionalProps", "appear", "in", "timeout", "role", "closeAfterTransition", "as", "onMouseDown", "elevation", "Provider", "value", "process", "env", "NODE_ENV", "propTypes", "string", "oneOfType", "oneOf", "bool", "object", "node", "func", "isRequired", "shape", "sx", "arrayOf", "number"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/capstone final project/SafeRoomAI/frontend/node_modules/@mui/material/Dialog/Dialog.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useId from '@mui/utils/useId';\nimport capitalize from \"../utils/capitalize.js\";\nimport Modal from \"../Modal/index.js\";\nimport Fade from \"../Fade/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport dialogClasses, { getDialogUtilityClass } from \"./dialogClasses.js\";\nimport DialogContext from \"./DialogContext.js\";\nimport Backdrop from \"../Backdrop/index.js\";\nimport { styled, useTheme } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DialogBackdrop = styled(Backdrop, {\n  name: 'MuiDialog',\n  slot: 'Backdrop',\n  overrides: (props, styles) => styles.backdrop\n})({\n  // Improve scrollable dialog support.\n  zIndex: -1\n});\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    scroll,\n    maxWidth,\n    fullWidth,\n    fullScreen\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    container: ['container', `scroll${capitalize(scroll)}`],\n    paper: ['paper', `paperScroll${capitalize(scroll)}`, `paperWidth${capitalize(String(maxWidth))}`, fullWidth && 'paperFullWidth', fullScreen && 'paperFullScreen']\n  };\n  return composeClasses(slots, getDialogUtilityClass, classes);\n};\nconst DialogRoot = styled(Modal, {\n  name: 'MuiDialog',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  '@media print': {\n    // Use !important to override the Modal inline-style.\n    position: 'absolute !important'\n  }\n});\nconst DialogContainer = styled('div', {\n  name: 'MuiDialog',\n  slot: 'Container',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.container, styles[`scroll${capitalize(ownerState.scroll)}`]];\n  }\n})({\n  height: '100%',\n  '@media print': {\n    height: 'auto'\n  },\n  // We disable the focus ring for mouse, touch and keyboard users.\n  outline: 0,\n  variants: [{\n    props: {\n      scroll: 'paper'\n    },\n    style: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center'\n    }\n  }, {\n    props: {\n      scroll: 'body'\n    },\n    style: {\n      overflowY: 'auto',\n      overflowX: 'hidden',\n      textAlign: 'center',\n      '&::after': {\n        content: '\"\"',\n        display: 'inline-block',\n        verticalAlign: 'middle',\n        height: '100%',\n        width: '0'\n      }\n    }\n  }]\n});\nconst DialogPaper = styled(Paper, {\n  name: 'MuiDialog',\n  slot: 'Paper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.paper, styles[`scrollPaper${capitalize(ownerState.scroll)}`], styles[`paperWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fullWidth && styles.paperFullWidth, ownerState.fullScreen && styles.paperFullScreen];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  margin: 32,\n  position: 'relative',\n  overflowY: 'auto',\n  '@media print': {\n    overflowY: 'visible',\n    boxShadow: 'none'\n  },\n  variants: [{\n    props: {\n      scroll: 'paper'\n    },\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      maxHeight: 'calc(100% - 64px)'\n    }\n  }, {\n    props: {\n      scroll: 'body'\n    },\n    style: {\n      display: 'inline-block',\n      verticalAlign: 'middle',\n      textAlign: 'initial'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.maxWidth,\n    style: {\n      maxWidth: 'calc(100% - 64px)'\n    }\n  }, {\n    props: {\n      maxWidth: 'xs'\n    },\n    style: {\n      maxWidth: theme.breakpoints.unit === 'px' ? Math.max(theme.breakpoints.values.xs, 444) : `max(${theme.breakpoints.values.xs}${theme.breakpoints.unit}, 444px)`,\n      [`&.${dialogClasses.paperScrollBody}`]: {\n        [theme.breakpoints.down(Math.max(theme.breakpoints.values.xs, 444) + 32 * 2)]: {\n          maxWidth: 'calc(100% - 64px)'\n        }\n      }\n    }\n  }, ...Object.keys(theme.breakpoints.values).filter(maxWidth => maxWidth !== 'xs').map(maxWidth => ({\n    props: {\n      maxWidth\n    },\n    style: {\n      maxWidth: `${theme.breakpoints.values[maxWidth]}${theme.breakpoints.unit}`,\n      [`&.${dialogClasses.paperScrollBody}`]: {\n        [theme.breakpoints.down(theme.breakpoints.values[maxWidth] + 32 * 2)]: {\n          maxWidth: 'calc(100% - 64px)'\n        }\n      }\n    }\n  })), {\n    props: ({\n      ownerState\n    }) => ownerState.fullWidth,\n    style: {\n      width: 'calc(100% - 64px)'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.fullScreen,\n    style: {\n      margin: 0,\n      width: '100%',\n      maxWidth: '100%',\n      height: '100%',\n      maxHeight: 'none',\n      borderRadius: 0,\n      [`&.${dialogClasses.paperScrollBody}`]: {\n        margin: 0,\n        maxWidth: '100%'\n      }\n    }\n  }]\n})));\n\n/**\n * Dialogs are overlaid modal paper based components with a backdrop.\n */\nconst Dialog = /*#__PURE__*/React.forwardRef(function Dialog(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDialog'\n  });\n  const theme = useTheme();\n  const defaultTransitionDuration = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n    'aria-describedby': ariaDescribedby,\n    'aria-labelledby': ariaLabelledbyProp,\n    'aria-modal': ariaModal = true,\n    BackdropComponent,\n    BackdropProps,\n    children,\n    className,\n    disableEscapeKeyDown = false,\n    fullScreen = false,\n    fullWidth = false,\n    maxWidth = 'sm',\n    onBackdropClick,\n    onClick,\n    onClose,\n    open,\n    PaperComponent = Paper,\n    PaperProps = {},\n    scroll = 'paper',\n    slots = {},\n    slotProps = {},\n    TransitionComponent = Fade,\n    transitionDuration = defaultTransitionDuration,\n    TransitionProps,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    disableEscapeKeyDown,\n    fullScreen,\n    fullWidth,\n    maxWidth,\n    scroll\n  };\n  const classes = useUtilityClasses(ownerState);\n  const backdropClick = React.useRef();\n  const handleMouseDown = event => {\n    // We don't want to close the dialog when clicking the dialog content.\n    // Make sure the event starts and ends on the same DOM element.\n    backdropClick.current = event.target === event.currentTarget;\n  };\n  const handleBackdropClick = event => {\n    if (onClick) {\n      onClick(event);\n    }\n\n    // Ignore the events not coming from the \"backdrop\".\n    if (!backdropClick.current) {\n      return;\n    }\n    backdropClick.current = null;\n    if (onBackdropClick) {\n      onBackdropClick(event);\n    }\n    if (onClose) {\n      onClose(event, 'backdropClick');\n    }\n  };\n  const ariaLabelledby = useId(ariaLabelledbyProp);\n  const dialogContextValue = React.useMemo(() => {\n    return {\n      titleId: ariaLabelledby\n    };\n  }, [ariaLabelledby]);\n  const backwardCompatibleSlots = {\n    transition: TransitionComponent,\n    ...slots\n  };\n  const backwardCompatibleSlotProps = {\n    transition: TransitionProps,\n    paper: PaperProps,\n    backdrop: BackdropProps,\n    ...slotProps\n  };\n  const externalForwardedProps = {\n    slots: backwardCompatibleSlots,\n    slotProps: backwardCompatibleSlotProps\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    elementType: DialogRoot,\n    shouldForwardComponentProp: true,\n    externalForwardedProps,\n    ownerState,\n    className: clsx(classes.root, className),\n    ref\n  });\n  const [BackdropSlot, backdropSlotProps] = useSlot('backdrop', {\n    elementType: DialogBackdrop,\n    shouldForwardComponentProp: true,\n    externalForwardedProps,\n    ownerState\n  });\n  const [PaperSlot, paperSlotProps] = useSlot('paper', {\n    elementType: DialogPaper,\n    shouldForwardComponentProp: true,\n    externalForwardedProps,\n    ownerState,\n    className: clsx(classes.paper, PaperProps.className)\n  });\n  const [ContainerSlot, containerSlotProps] = useSlot('container', {\n    elementType: DialogContainer,\n    externalForwardedProps,\n    ownerState,\n    className: clsx(classes.container)\n  });\n  const [TransitionSlot, transitionSlotProps] = useSlot('transition', {\n    elementType: Fade,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      appear: true,\n      in: open,\n      timeout: transitionDuration,\n      role: 'presentation'\n    }\n  });\n  return /*#__PURE__*/_jsx(RootSlot, {\n    closeAfterTransition: true,\n    slots: {\n      backdrop: BackdropSlot\n    },\n    slotProps: {\n      backdrop: {\n        transitionDuration,\n        as: BackdropComponent,\n        ...backdropSlotProps\n      }\n    },\n    disableEscapeKeyDown: disableEscapeKeyDown,\n    onClose: onClose,\n    open: open,\n    onClick: handleBackdropClick,\n    ...rootSlotProps,\n    ...other,\n    children: /*#__PURE__*/_jsx(TransitionSlot, {\n      ...transitionSlotProps,\n      children: /*#__PURE__*/_jsx(ContainerSlot, {\n        onMouseDown: handleMouseDown,\n        ...containerSlotProps,\n        children: /*#__PURE__*/_jsx(PaperSlot, {\n          as: PaperComponent,\n          elevation: 24,\n          role: \"dialog\",\n          \"aria-describedby\": ariaDescribedby,\n          \"aria-labelledby\": ariaLabelledby,\n          \"aria-modal\": ariaModal,\n          ...paperSlotProps,\n          children: /*#__PURE__*/_jsx(DialogContext.Provider, {\n            value: dialogContextValue,\n            children: children\n          })\n        })\n      })\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Dialog.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The id(s) of the element(s) that describe the dialog.\n   */\n  'aria-describedby': PropTypes.string,\n  /**\n   * The id(s) of the element(s) that label the dialog.\n   */\n  'aria-labelledby': PropTypes.string,\n  /**\n   * Informs assistive technologies that the element is modal.\n   * It's added on the element with role=\"dialog\".\n   * @default true\n   */\n  'aria-modal': PropTypes.oneOfType([PropTypes.oneOf(['false', 'true']), PropTypes.bool]),\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. While this prop currently works, it will be removed in the next major version.\n   * Use the `slots.backdrop` prop to make your application ready for the next version of Material UI.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   *   overridesResolver: (props, styles) => {\n   *     return styles.backdrop;\n   *   },\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * @ignore\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * Dialog children, usually the included sub-components.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, hitting escape will not fire the `onClose` callback.\n   * @default false\n   */\n  disableEscapeKeyDown: PropTypes.bool,\n  /**\n   * If `true`, the dialog is full-screen.\n   * @default false\n   */\n  fullScreen: PropTypes.bool,\n  /**\n   * If `true`, the dialog stretches to `maxWidth`.\n   *\n   * Notice that the dialog width grow is limited by the default margin.\n   * @default false\n   */\n  fullWidth: PropTypes.bool,\n  /**\n   * Determine the max-width of the dialog.\n   * The dialog width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'sm'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * Callback fired when the backdrop is clicked.\n   * @deprecated Use the `onClose` prop with the `reason` argument to handle the `backdropClick` events.\n   */\n  onBackdropClick: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The component used to render the body of the dialog.\n   * @default Paper\n   */\n  PaperComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Paper`](https://mui.com/material-ui/api/paper/) element.\n   * @default {}\n   * @deprecated Use `slotProps.paper` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  PaperProps: PropTypes.object,\n  /**\n   * Determine the container for scrolling the dialog.\n   * @default 'paper'\n   */\n  scroll: PropTypes.oneOf(['body', 'paper']),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    container: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    paper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    container: PropTypes.elementType,\n    paper: PropTypes.elementType,\n    root: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Fade\n   * @deprecated Use `slots.transition` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })]),\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @deprecated Use `slotProps.transition` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Dialog;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,aAAa,IAAIC,qBAAqB,QAAQ,oBAAoB;AACzE,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,MAAM,EAAEC,QAAQ,QAAQ,yBAAyB;AAC1D,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAGP,MAAM,CAACD,QAAQ,EAAE;EACtCS,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;AACvC,CAAC,CAAC,CAAC;EACD;EACAC,MAAM,EAAE,CAAC;AACX,CAAC,CAAC;AACF,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,MAAM;IACNC,QAAQ;IACRC,SAAS;IACTC;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,SAAS,EAAE,CAAC,WAAW,EAAE,SAAShC,UAAU,CAAC0B,MAAM,CAAC,EAAE,CAAC;IACvDO,KAAK,EAAE,CAAC,OAAO,EAAE,cAAcjC,UAAU,CAAC0B,MAAM,CAAC,EAAE,EAAE,aAAa1B,UAAU,CAACkC,MAAM,CAACP,QAAQ,CAAC,CAAC,EAAE,EAAEC,SAAS,IAAI,gBAAgB,EAAEC,UAAU,IAAI,iBAAiB;EAClK,CAAC;EACD,OAAO/B,cAAc,CAACgC,KAAK,EAAEzB,qBAAqB,EAAEoB,OAAO,CAAC;AAC9D,CAAC;AACD,MAAMU,UAAU,GAAG3B,MAAM,CAACP,KAAK,EAAE;EAC/Be,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZmB,iBAAiB,EAAEA,CAACjB,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACW;AAC/C,CAAC,CAAC,CAAC;EACD,cAAc,EAAE;IACd;IACAM,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AACF,MAAMC,eAAe,GAAG9B,MAAM,CAAC,KAAK,EAAE;EACpCQ,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,WAAW;EACjBmB,iBAAiB,EAAEA,CAACjB,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJI;IACF,CAAC,GAAGL,KAAK;IACT,OAAO,CAACC,MAAM,CAACY,SAAS,EAAEZ,MAAM,CAAC,SAASpB,UAAU,CAACwB,UAAU,CAACE,MAAM,CAAC,EAAE,CAAC,CAAC;EAC7E;AACF,CAAC,CAAC,CAAC;EACDa,MAAM,EAAE,MAAM;EACd,cAAc,EAAE;IACdA,MAAM,EAAE;EACV,CAAC;EACD;EACAC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,CAAC;IACTtB,KAAK,EAAE;MACLO,MAAM,EAAE;IACV,CAAC;IACDgB,KAAK,EAAE;MACLC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE;IACd;EACF,CAAC,EAAE;IACD1B,KAAK,EAAE;MACLO,MAAM,EAAE;IACV,CAAC;IACDgB,KAAK,EAAE;MACLI,SAAS,EAAE,MAAM;MACjBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,QAAQ;MACnB,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbN,OAAO,EAAE,cAAc;QACvBO,aAAa,EAAE,QAAQ;QACvBX,MAAM,EAAE,MAAM;QACdY,KAAK,EAAE;MACT;IACF;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,WAAW,GAAG5C,MAAM,CAACL,KAAK,EAAE;EAChCa,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,OAAO;EACbmB,iBAAiB,EAAEA,CAACjB,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJI;IACF,CAAC,GAAGL,KAAK;IACT,OAAO,CAACC,MAAM,CAACa,KAAK,EAAEb,MAAM,CAAC,cAAcpB,UAAU,CAACwB,UAAU,CAACE,MAAM,CAAC,EAAE,CAAC,EAAEN,MAAM,CAAC,aAAapB,UAAU,CAACkC,MAAM,CAACV,UAAU,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEH,UAAU,CAACI,SAAS,IAAIR,MAAM,CAACiC,cAAc,EAAE7B,UAAU,CAACK,UAAU,IAAIT,MAAM,CAACkC,eAAe,CAAC;EAC9O;AACF,CAAC,CAAC,CAAC5C,SAAS,CAAC,CAAC;EACZ6C;AACF,CAAC,MAAM;EACLC,MAAM,EAAE,EAAE;EACVnB,QAAQ,EAAE,UAAU;EACpBS,SAAS,EAAE,MAAM;EACjB,cAAc,EAAE;IACdA,SAAS,EAAE,SAAS;IACpBW,SAAS,EAAE;EACb,CAAC;EACDhB,QAAQ,EAAE,CAAC;IACTtB,KAAK,EAAE;MACLO,MAAM,EAAE;IACV,CAAC;IACDgB,KAAK,EAAE;MACLC,OAAO,EAAE,MAAM;MACfe,aAAa,EAAE,QAAQ;MACvBC,SAAS,EAAE;IACb;EACF,CAAC,EAAE;IACDxC,KAAK,EAAE;MACLO,MAAM,EAAE;IACV,CAAC;IACDgB,KAAK,EAAE;MACLC,OAAO,EAAE,cAAc;MACvBO,aAAa,EAAE,QAAQ;MACvBF,SAAS,EAAE;IACb;EACF,CAAC,EAAE;IACD7B,KAAK,EAAEA,CAAC;MACNK;IACF,CAAC,KAAK,CAACA,UAAU,CAACG,QAAQ;IAC1Be,KAAK,EAAE;MACLf,QAAQ,EAAE;IACZ;EACF,CAAC,EAAE;IACDR,KAAK,EAAE;MACLQ,QAAQ,EAAE;IACZ,CAAC;IACDe,KAAK,EAAE;MACLf,QAAQ,EAAE4B,KAAK,CAACK,WAAW,CAACC,IAAI,KAAK,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACR,KAAK,CAACK,WAAW,CAACI,MAAM,CAACC,EAAE,EAAE,GAAG,CAAC,GAAG,OAAOV,KAAK,CAACK,WAAW,CAACI,MAAM,CAACC,EAAE,GAAGV,KAAK,CAACK,WAAW,CAACC,IAAI,UAAU;MAC9J,CAAC,KAAKzD,aAAa,CAAC8D,eAAe,EAAE,GAAG;QACtC,CAACX,KAAK,CAACK,WAAW,CAACO,IAAI,CAACL,IAAI,CAACC,GAAG,CAACR,KAAK,CAACK,WAAW,CAACI,MAAM,CAACC,EAAE,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG;UAC7EtC,QAAQ,EAAE;QACZ;MACF;IACF;EACF,CAAC,EAAE,GAAGyC,MAAM,CAACC,IAAI,CAACd,KAAK,CAACK,WAAW,CAACI,MAAM,CAAC,CAACM,MAAM,CAAC3C,QAAQ,IAAIA,QAAQ,KAAK,IAAI,CAAC,CAAC4C,GAAG,CAAC5C,QAAQ,KAAK;IACjGR,KAAK,EAAE;MACLQ;IACF,CAAC;IACDe,KAAK,EAAE;MACLf,QAAQ,EAAE,GAAG4B,KAAK,CAACK,WAAW,CAACI,MAAM,CAACrC,QAAQ,CAAC,GAAG4B,KAAK,CAACK,WAAW,CAACC,IAAI,EAAE;MAC1E,CAAC,KAAKzD,aAAa,CAAC8D,eAAe,EAAE,GAAG;QACtC,CAACX,KAAK,CAACK,WAAW,CAACO,IAAI,CAACZ,KAAK,CAACK,WAAW,CAACI,MAAM,CAACrC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG;UACrEA,QAAQ,EAAE;QACZ;MACF;IACF;EACF,CAAC,CAAC,CAAC,EAAE;IACHR,KAAK,EAAEA,CAAC;MACNK;IACF,CAAC,KAAKA,UAAU,CAACI,SAAS;IAC1Bc,KAAK,EAAE;MACLS,KAAK,EAAE;IACT;EACF,CAAC,EAAE;IACDhC,KAAK,EAAEA,CAAC;MACNK;IACF,CAAC,KAAKA,UAAU,CAACK,UAAU;IAC3Ba,KAAK,EAAE;MACLc,MAAM,EAAE,CAAC;MACTL,KAAK,EAAE,MAAM;MACbxB,QAAQ,EAAE,MAAM;MAChBY,MAAM,EAAE,MAAM;MACdoB,SAAS,EAAE,MAAM;MACjBa,YAAY,EAAE,CAAC;MACf,CAAC,KAAKpE,aAAa,CAAC8D,eAAe,EAAE,GAAG;QACtCV,MAAM,EAAE,CAAC;QACT7B,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;;AAEJ;AACA;AACA;AACA,MAAM8C,MAAM,GAAG,aAAa9E,KAAK,CAAC+E,UAAU,CAAC,SAASD,MAAMA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzE,MAAMzD,KAAK,GAAGR,eAAe,CAAC;IAC5BQ,KAAK,EAAEwD,OAAO;IACd3D,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMuC,KAAK,GAAG9C,QAAQ,CAAC,CAAC;EACxB,MAAMoE,yBAAyB,GAAG;IAChCC,KAAK,EAAEvB,KAAK,CAACwB,WAAW,CAACC,QAAQ,CAACC,cAAc;IAChDC,IAAI,EAAE3B,KAAK,CAACwB,WAAW,CAACC,QAAQ,CAACG;EACnC,CAAC;EACD,MAAM;IACJ,kBAAkB,EAAEC,eAAe;IACnC,iBAAiB,EAAEC,kBAAkB;IACrC,YAAY,EAAEC,SAAS,GAAG,IAAI;IAC9BC,iBAAiB;IACjBC,aAAa;IACbC,QAAQ;IACRC,SAAS;IACTC,oBAAoB,GAAG,KAAK;IAC5B9D,UAAU,GAAG,KAAK;IAClBD,SAAS,GAAG,KAAK;IACjBD,QAAQ,GAAG,IAAI;IACfiE,eAAe;IACfC,OAAO;IACPC,OAAO;IACPC,IAAI;IACJC,cAAc,GAAG7F,KAAK;IACtB8F,UAAU,GAAG,CAAC,CAAC;IACfvE,MAAM,GAAG,OAAO;IAChBI,KAAK,GAAG,CAAC,CAAC;IACVoE,SAAS,GAAG,CAAC,CAAC;IACdC,mBAAmB,GAAGjG,IAAI;IAC1BkG,kBAAkB,GAAGvB,yBAAyB;IAC9CwB,eAAe;IACf,GAAGC;EACL,CAAC,GAAGnF,KAAK;EACT,MAAMK,UAAU,GAAG;IACjB,GAAGL,KAAK;IACRwE,oBAAoB;IACpB9D,UAAU;IACVD,SAAS;IACTD,QAAQ;IACRD;EACF,CAAC;EACD,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM+E,aAAa,GAAG5G,KAAK,CAAC6G,MAAM,CAAC,CAAC;EACpC,MAAMC,eAAe,GAAGC,KAAK,IAAI;IAC/B;IACA;IACAH,aAAa,CAACI,OAAO,GAAGD,KAAK,CAACE,MAAM,KAAKF,KAAK,CAACG,aAAa;EAC9D,CAAC;EACD,MAAMC,mBAAmB,GAAGJ,KAAK,IAAI;IACnC,IAAIb,OAAO,EAAE;MACXA,OAAO,CAACa,KAAK,CAAC;IAChB;;IAEA;IACA,IAAI,CAACH,aAAa,CAACI,OAAO,EAAE;MAC1B;IACF;IACAJ,aAAa,CAACI,OAAO,GAAG,IAAI;IAC5B,IAAIf,eAAe,EAAE;MACnBA,eAAe,CAACc,KAAK,CAAC;IACxB;IACA,IAAIZ,OAAO,EAAE;MACXA,OAAO,CAACY,KAAK,EAAE,eAAe,CAAC;IACjC;EACF,CAAC;EACD,MAAMK,cAAc,GAAGhH,KAAK,CAACsF,kBAAkB,CAAC;EAChD,MAAM2B,kBAAkB,GAAGrH,KAAK,CAACsH,OAAO,CAAC,MAAM;IAC7C,OAAO;MACLC,OAAO,EAAEH;IACX,CAAC;EACH,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EACpB,MAAMI,uBAAuB,GAAG;IAC9BC,UAAU,EAAEjB,mBAAmB;IAC/B,GAAGrE;EACL,CAAC;EACD,MAAMuF,2BAA2B,GAAG;IAClCD,UAAU,EAAEf,eAAe;IAC3BpE,KAAK,EAAEgE,UAAU;IACjB5E,QAAQ,EAAEmE,aAAa;IACvB,GAAGU;EACL,CAAC;EACD,MAAMoB,sBAAsB,GAAG;IAC7BxF,KAAK,EAAEqF,uBAAuB;IAC9BjB,SAAS,EAAEmB;EACb,CAAC;EACD,MAAM,CAACE,QAAQ,EAAEC,aAAa,CAAC,GAAG5G,OAAO,CAAC,MAAM,EAAE;IAChD6G,WAAW,EAAEtF,UAAU;IACvBuF,0BAA0B,EAAE,IAAI;IAChCJ,sBAAsB;IACtB9F,UAAU;IACVkE,SAAS,EAAE7F,IAAI,CAAC4B,OAAO,CAACM,IAAI,EAAE2D,SAAS,CAAC;IACxCd;EACF,CAAC,CAAC;EACF,MAAM,CAAC+C,YAAY,EAAEC,iBAAiB,CAAC,GAAGhH,OAAO,CAAC,UAAU,EAAE;IAC5D6G,WAAW,EAAE1G,cAAc;IAC3B2G,0BAA0B,EAAE,IAAI;IAChCJ,sBAAsB;IACtB9F;EACF,CAAC,CAAC;EACF,MAAM,CAACqG,SAAS,EAAEC,cAAc,CAAC,GAAGlH,OAAO,CAAC,OAAO,EAAE;IACnD6G,WAAW,EAAErE,WAAW;IACxBsE,0BAA0B,EAAE,IAAI;IAChCJ,sBAAsB;IACtB9F,UAAU;IACVkE,SAAS,EAAE7F,IAAI,CAAC4B,OAAO,CAACQ,KAAK,EAAEgE,UAAU,CAACP,SAAS;EACrD,CAAC,CAAC;EACF,MAAM,CAACqC,aAAa,EAAEC,kBAAkB,CAAC,GAAGpH,OAAO,CAAC,WAAW,EAAE;IAC/D6G,WAAW,EAAEnF,eAAe;IAC5BgF,sBAAsB;IACtB9F,UAAU;IACVkE,SAAS,EAAE7F,IAAI,CAAC4B,OAAO,CAACO,SAAS;EACnC,CAAC,CAAC;EACF,MAAM,CAACiG,cAAc,EAAEC,mBAAmB,CAAC,GAAGtH,OAAO,CAAC,YAAY,EAAE;IAClE6G,WAAW,EAAEvH,IAAI;IACjBoH,sBAAsB;IACtB9F,UAAU;IACV2G,eAAe,EAAE;MACfC,MAAM,EAAE,IAAI;MACZC,EAAE,EAAEtC,IAAI;MACRuC,OAAO,EAAElC,kBAAkB;MAC3BmC,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EACF,OAAO,aAAazH,IAAI,CAACyG,QAAQ,EAAE;IACjCiB,oBAAoB,EAAE,IAAI;IAC1B1G,KAAK,EAAE;MACLT,QAAQ,EAAEsG;IACZ,CAAC;IACDzB,SAAS,EAAE;MACT7E,QAAQ,EAAE;QACR+E,kBAAkB;QAClBqC,EAAE,EAAElD,iBAAiB;QACrB,GAAGqC;MACL;IACF,CAAC;IACDjC,oBAAoB,EAAEA,oBAAoB;IAC1CG,OAAO,EAAEA,OAAO;IAChBC,IAAI,EAAEA,IAAI;IACVF,OAAO,EAAEiB,mBAAmB;IAC5B,GAAGU,aAAa;IAChB,GAAGlB,KAAK;IACRb,QAAQ,EAAE,aAAa3E,IAAI,CAACmH,cAAc,EAAE;MAC1C,GAAGC,mBAAmB;MACtBzC,QAAQ,EAAE,aAAa3E,IAAI,CAACiH,aAAa,EAAE;QACzCW,WAAW,EAAEjC,eAAe;QAC5B,GAAGuB,kBAAkB;QACrBvC,QAAQ,EAAE,aAAa3E,IAAI,CAAC+G,SAAS,EAAE;UACrCY,EAAE,EAAEzC,cAAc;UAClB2C,SAAS,EAAE,EAAE;UACbJ,IAAI,EAAE,QAAQ;UACd,kBAAkB,EAAEnD,eAAe;UACnC,iBAAiB,EAAE2B,cAAc;UACjC,YAAY,EAAEzB,SAAS;UACvB,GAAGwC,cAAc;UACjBrC,QAAQ,EAAE,aAAa3E,IAAI,CAACR,aAAa,CAACsI,QAAQ,EAAE;YAClDC,KAAK,EAAE7B,kBAAkB;YACzBvB,QAAQ,EAAEA;UACZ,CAAC;QACH,CAAC;MACH,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFqD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvE,MAAM,CAACwE,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;EACE,kBAAkB,EAAErJ,SAAS,CAACsJ,MAAM;EACpC;AACF;AACA;EACE,iBAAiB,EAAEtJ,SAAS,CAACsJ,MAAM;EACnC;AACF;AACA;AACA;AACA;EACE,YAAY,EAAEtJ,SAAS,CAACuJ,SAAS,CAAC,CAACvJ,SAAS,CAACwJ,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,EAAExJ,SAAS,CAACyJ,IAAI,CAAC,CAAC;EACvF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE9D,iBAAiB,EAAE3F,SAAS,CAAC6H,WAAW;EACxC;AACF;AACA;EACEjC,aAAa,EAAE5F,SAAS,CAAC0J,MAAM;EAC/B;AACF;AACA;EACE7D,QAAQ,EAAE7F,SAAS,CAAC2J,IAAI;EACxB;AACF;AACA;EACE9H,OAAO,EAAE7B,SAAS,CAAC0J,MAAM;EACzB;AACF;AACA;EACE5D,SAAS,EAAE9F,SAAS,CAACsJ,MAAM;EAC3B;AACF;AACA;AACA;EACEvD,oBAAoB,EAAE/F,SAAS,CAACyJ,IAAI;EACpC;AACF;AACA;AACA;EACExH,UAAU,EAAEjC,SAAS,CAACyJ,IAAI;EAC1B;AACF;AACA;AACA;AACA;AACA;EACEzH,SAAS,EAAEhC,SAAS,CAACyJ,IAAI;EACzB;AACF;AACA;AACA;AACA;AACA;EACE1H,QAAQ,EAAE/B,SAAS,CAAC,sCAAsCuJ,SAAS,CAAC,CAACvJ,SAAS,CAACwJ,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,EAAExJ,SAAS,CAACsJ,MAAM,CAAC,CAAC;EAC/I;AACF;AACA;AACA;EACEtD,eAAe,EAAEhG,SAAS,CAAC4J,IAAI;EAC/B;AACF;AACA;EACE3D,OAAO,EAAEjG,SAAS,CAAC4J,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;EACE1D,OAAO,EAAElG,SAAS,CAAC4J,IAAI;EACvB;AACF;AACA;EACEzD,IAAI,EAAEnG,SAAS,CAACyJ,IAAI,CAACI,UAAU;EAC/B;AACF;AACA;AACA;EACEzD,cAAc,EAAEpG,SAAS,CAAC6H,WAAW;EACrC;AACF;AACA;AACA;AACA;EACExB,UAAU,EAAErG,SAAS,CAAC0J,MAAM;EAC5B;AACF;AACA;AACA;EACE5H,MAAM,EAAE9B,SAAS,CAACwJ,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;EAC1C;AACF;AACA;AACA;EACElD,SAAS,EAAEtG,SAAS,CAAC8J,KAAK,CAAC;IACzBrI,QAAQ,EAAEzB,SAAS,CAACuJ,SAAS,CAAC,CAACvJ,SAAS,CAAC4J,IAAI,EAAE5J,SAAS,CAAC0J,MAAM,CAAC,CAAC;IACjEtH,SAAS,EAAEpC,SAAS,CAACuJ,SAAS,CAAC,CAACvJ,SAAS,CAAC4J,IAAI,EAAE5J,SAAS,CAAC0J,MAAM,CAAC,CAAC;IAClErH,KAAK,EAAErC,SAAS,CAACuJ,SAAS,CAAC,CAACvJ,SAAS,CAAC4J,IAAI,EAAE5J,SAAS,CAAC0J,MAAM,CAAC,CAAC;IAC9DvH,IAAI,EAAEnC,SAAS,CAACuJ,SAAS,CAAC,CAACvJ,SAAS,CAAC4J,IAAI,EAAE5J,SAAS,CAAC0J,MAAM,CAAC,CAAC;IAC7DlC,UAAU,EAAExH,SAAS,CAACuJ,SAAS,CAAC,CAACvJ,SAAS,CAAC4J,IAAI,EAAE5J,SAAS,CAAC0J,MAAM,CAAC;EACpE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACExH,KAAK,EAAElC,SAAS,CAAC8J,KAAK,CAAC;IACrBrI,QAAQ,EAAEzB,SAAS,CAAC6H,WAAW;IAC/BzF,SAAS,EAAEpC,SAAS,CAAC6H,WAAW;IAChCxF,KAAK,EAAErC,SAAS,CAAC6H,WAAW;IAC5B1F,IAAI,EAAEnC,SAAS,CAAC6H,WAAW;IAC3BL,UAAU,EAAExH,SAAS,CAAC6H;EACxB,CAAC,CAAC;EACF;AACF;AACA;EACEkC,EAAE,EAAE/J,SAAS,CAACuJ,SAAS,CAAC,CAACvJ,SAAS,CAACgK,OAAO,CAAChK,SAAS,CAACuJ,SAAS,CAAC,CAACvJ,SAAS,CAAC4J,IAAI,EAAE5J,SAAS,CAAC0J,MAAM,EAAE1J,SAAS,CAACyJ,IAAI,CAAC,CAAC,CAAC,EAAEzJ,SAAS,CAAC4J,IAAI,EAAE5J,SAAS,CAAC0J,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;EACEnD,mBAAmB,EAAEvG,SAAS,CAAC6H,WAAW;EAC1C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACErB,kBAAkB,EAAExG,SAAS,CAACuJ,SAAS,CAAC,CAACvJ,SAAS,CAACiK,MAAM,EAAEjK,SAAS,CAAC8J,KAAK,CAAC;IACzEtB,MAAM,EAAExI,SAAS,CAACiK,MAAM;IACxB/E,KAAK,EAAElF,SAAS,CAACiK,MAAM;IACvB3E,IAAI,EAAEtF,SAAS,CAACiK;EAClB,CAAC,CAAC,CAAC,CAAC;EACJ;AACF;AACA;AACA;AACA;EACExD,eAAe,EAAEzG,SAAS,CAAC0J;AAC7B,CAAC,GAAG,KAAK,CAAC;AACV,eAAe7E,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}